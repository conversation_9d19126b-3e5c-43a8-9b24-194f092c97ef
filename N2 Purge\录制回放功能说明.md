# N2Purge 录制回放功能说明

## 功能概述

N2Purge项目已集成录制回放功能，支持通过可配置的键盘快捷键控制录制和回放操作。

## 快捷键说明（可配置）

**默认热键配置**：
- **Ctrl+Shift+S**: 开始录制
- **Ctrl+Shift+G**: 停止录制
- **Ctrl+Shift+P**: 开始回放（回放最新的录制文件）
- **Ctrl+Shift+\\**: 停止回放
- **Ctrl+Shift+C**: 清除所有传送带状态（调试用）

**配置文件位置**: `Config/RecordReplayConfig.json`

## 功能特性

### 录制功能
- 支持鼠标和键盘操作录制
- 自动保存录制文件到 `RecordReplay` 目录
- 录制文件格式为JSON，便于查看和分析
- 支持录制状态监控和日志记录

### 回放功能
- 支持回放最新录制的操作序列
- 可以随时停止回放
- 回放过程中会在日志中显示详细信息

### 安全机制
- 录制和回放互斥，不能同时进行
- 完善的错误处理和异常捕获
- 详细的日志记录，便于调试

## 使用方法

1. **启动应用程序**
   - 运行N2Purge应用程序
   - 系统会自动初始化录制回放服务

2. **开始录制**
   - 按下 `Ctrl+Shift+S` 组合键开始录制
   - 日志中会显示"开始录制"信息和热键配置
   - 此时所有的鼠标和键盘操作都会被记录

3. **停止录制**
   - 按下 `Ctrl+Shift+G` 组合键停止录制
   - 录制文件会自动保存到 `RecordReplay` 目录
   - 日志中会显示保存的文件路径

4. **开始回放**
   - 按下 `Ctrl+Shift+P` 组合键开始回放最新的录制
   - 系统会自动查找最新的录制文件并开始回放
   - 回放过程中会在日志中显示操作信息

5. **停止回放**
   - 按下 `Ctrl+Shift+\` 组合键停止当前回放
   - 日志中会显示"回放已停止"信息

## 文件存储

- **存储目录**: `应用程序目录/RecordReplay/`
- **文件命名**: `Recording_yyyyMMdd_HHmmss.json`
- **文件格式**: JSON格式，包含录制开始时间、结束时间和操作列表

## 日志信息

所有录制回放相关的操作都会记录在日志中，包括：
- 服务初始化状态
- 录制开始/停止
- 回放开始/停止
- 错误和异常信息
- 键盘事件触发信息

## 热键配置

### 配置文件格式

配置文件位于 `Config/RecordReplayConfig.json`，可以自定义热键组合：

```json
{
  "HotKeys": {
    "StartRecording": {
      "Key": "S",
      "Modifiers": ["Ctrl", "Shift"],
      "Description": "开始录制"
    },
    "StopRecording": {
      "Key": "G",
      "Modifiers": ["Ctrl", "Shift"],
      "Description": "停止录制"
    },
    "StartPlayback": {
      "Key": "P",
      "Modifiers": ["Ctrl", "Shift"],
      "Description": "开始回放"
    },
    "StopPlayback": {
      "Key": "OemPipe",
      "Modifiers": ["Ctrl", "Shift"],
      "Description": "停止回放 (Ctrl+Shift+\\)"
    }
  }
}
```

### 支持的按键

- **主键**: 任何有效的WPF Key枚举值（如 A, B, C, F1, F2, Enter, Space, OemPipe等）
- **修饰键**: Ctrl, Shift, Alt 的任意组合

### 修改热键

1. 编辑 `Config/RecordReplayConfig.json` 文件
2. 修改对应的 Key 和 Modifiers 值
3. 重启应用程序使配置生效

## 注意事项

1. **窗口焦点**: 确保N2Purge主窗口有焦点才能接收键盘事件
2. **文件权限**: 确保应用程序有权限在程序目录下创建和写入文件
3. **录制内容**: 当前版本主要录制鼠标和键盘事件的基本信息
4. **回放精度**: 简化版本的回放主要用于演示，实际的鼠标键盘模拟需要更复杂的实现
5. **热键冲突**: 避免与系统或其他应用程序的热键冲突

## 故障排除

### 热键无响应
1. 检查主窗口是否有焦点
2. 查看日志中是否有"键盘事件触发"信息，确认按键和修饰键是否正确
3. 确认录制回放服务是否正确初始化
4. 检查配置文件格式是否正确
5. 查看日志中的热键配置信息

### 录制文件未生成
1. 检查应用程序目录权限
2. 查看日志中的错误信息
3. 确认录制过程中是否有操作

### 回放无法开始
1. 确认RecordReplay目录下是否有录制文件
2. 检查录制文件格式是否正确
3. 查看日志中的详细错误信息

## 技术实现

- **服务类**: `SimpleRecordReplayService` - 核心录制回放功能
- **配置管理**: `RecordReplayConfigManager` - 热键配置管理
- **全局实例**: `GlobalData.recordReplayService` - 全局服务实例
- **事件处理**: 在MainWindow中处理可配置的键盘事件
- **配置文件**: `Config/RecordReplayConfig.json` - JSON格式配置
- **文件格式**: JSON序列化存储录制数据
- **日志组件**: 使用Proj.Log组件记录操作日志

### 主要文件

```
N2 Purge/
├── Config/
│   └── RecordReplayConfig.json          # 热键和功能配置
├── Services/
│   ├── SimpleRecordReplayService.cs     # 录制回放服务
│   └── RecordReplayConfigManager.cs     # 配置管理器
├── MainWindow.xaml.cs                   # 键盘事件处理
├── GlobalData.cs                        # 全局服务实例
└── 录制回放功能说明.md                  # 使用说明
```
