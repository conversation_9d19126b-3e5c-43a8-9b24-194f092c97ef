# N2Purge 录制回放功能说明

## 功能概述

N2Purge项目已集成录制回放功能，支持通过键盘快捷键控制录制和回放操作。

## 快捷键说明

- **F9**: 开始录制
- **F10**: 停止录制
- **F11**: 开始回放（回放最新的录制文件）
- **F12**: 停止回放

## 功能特性

### 录制功能
- 支持鼠标和键盘操作录制
- 自动保存录制文件到 `RecordReplay` 目录
- 录制文件格式为JSON，便于查看和分析
- 支持录制状态监控和日志记录

### 回放功能
- 支持回放最新录制的操作序列
- 可以随时停止回放
- 回放过程中会在日志中显示详细信息

### 安全机制
- 录制和回放互斥，不能同时进行
- 完善的错误处理和异常捕获
- 详细的日志记录，便于调试

## 使用方法

1. **启动应用程序**
   - 运行N2Purge应用程序
   - 系统会自动初始化录制回放服务

2. **开始录制**
   - 按下 `F9` 键开始录制
   - 日志中会显示"开始录制"信息
   - 此时所有的鼠标和键盘操作都会被记录

3. **停止录制**
   - 按下 `F10` 键停止录制
   - 录制文件会自动保存到 `RecordReplay` 目录
   - 日志中会显示保存的文件路径

4. **开始回放**
   - 按下 `F11` 键开始回放最新的录制
   - 系统会自动查找最新的录制文件并开始回放
   - 回放过程中会在日志中显示操作信息

5. **停止回放**
   - 按下 `F12` 键停止当前回放
   - 日志中会显示"回放已停止"信息

## 文件存储

- **存储目录**: `应用程序目录/RecordReplay/`
- **文件命名**: `Recording_yyyyMMdd_HHmmss.json`
- **文件格式**: JSON格式，包含录制开始时间、结束时间和操作列表

## 日志信息

所有录制回放相关的操作都会记录在日志中，包括：
- 服务初始化状态
- 录制开始/停止
- 回放开始/停止
- 错误和异常信息
- 键盘事件触发信息

## 注意事项

1. **窗口焦点**: 确保N2Purge主窗口有焦点才能接收键盘事件
2. **文件权限**: 确保应用程序有权限在程序目录下创建和写入文件
3. **录制内容**: 当前版本主要录制鼠标和键盘事件的基本信息
4. **回放精度**: 简化版本的回放主要用于演示，实际的鼠标键盘模拟需要更复杂的实现

## 故障排除

### F9键无响应
1. 检查主窗口是否有焦点
2. 查看日志中是否有"键盘事件触发"信息
3. 确认录制回放服务是否正确初始化

### 录制文件未生成
1. 检查应用程序目录权限
2. 查看日志中的错误信息
3. 确认录制过程中是否有操作

### 回放无法开始
1. 确认RecordReplay目录下是否有录制文件
2. 检查录制文件格式是否正确
3. 查看日志中的详细错误信息

## 技术实现

- **服务类**: `SimpleRecordReplayService`
- **全局实例**: `GlobalData.recordReplayService`
- **事件处理**: 在MainWindow中处理键盘事件
- **文件格式**: JSON序列化存储
- **日志组件**: 使用Proj.Log组件记录操作日志
