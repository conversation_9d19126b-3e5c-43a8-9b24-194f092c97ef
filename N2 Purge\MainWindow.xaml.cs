using N2Purge.ViewModel;
using N2Purge;
using N2Purge.frmUserControl;
using N2Purge.silan;
using N2Purge.userControls;
using N2Purge.ViewModel;
using System.Reflection;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DBEntity;
using System.Windows.Threading;
using Proj.WCF;
using PlcTools;
using N2Purge.Conveyor;
using N2Purge.Services;
using Proj.Log;

namespace N2Purge
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        private bool _recordReplayInitialized = false;
        private RecordReplayConfigManager _configManager;

        public MainWindow()
        {
            InitializeComponent();

            // 确保窗口可以接收键盘事件
            this.Focusable = true;
            this.Focus();

            // 初始化配置管理器
            _configManager = RecordReplayConfigManager.Instance;
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
          
            this.WindowState = WindowState.Maximized;
            //Thread.Sleep(1000);
          //  MoveCrane();
            CmdControl cmdControl = new CmdControl();
            cmdControl.Visibility= Visibility.Collapsed;
           // bdbtn.Child = cmdControl;
            GlobalData.ControlLeftClick += GlobalData_ShelfClick;
            FrmHelper.Show<frmUserCtr>(bd, sender);
            GlobalData.IsUserLogin += GlobalData_IsUserLogin;
            //  GlobalData.gbsignalhelper=new N2Purge.CommunicationHelper.SignalirHelper(GlobalData.ServerUrl);
            btn(false);
            WCFClient.Instance.Connect();
           
            GlobalData.gbPlcHelper.LoadPlcConfig(System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\PlcConfig.json"));
           // GlobalData.PlcConnect();

            // 初始化录制回放服务
            InitializeRecordReplayAsync();

            // 确保窗口获得焦点以接收键盘事件
            this.Focus();
        }
      
        private void btn(bool enable)
        {
            btnmain.IsEnabled=enable;
            btnSetting.IsEnabled=enable;
            btnMonitor.IsEnabled=enable;
            btnHistory.IsEnabled=enable;
            btnIO.IsEnabled=enable;
        }
        //用户登录后逻辑
        private void GlobalData_IsUserLogin(bool obj)
        {
            if (obj)
            {
                btn(true);
                Button_Click(null, null);
            }
            else 
            {
                btn(false);
            }
        }

        private void GlobalData_ShelfClick(string obj)
        {
            //var d = bdbtn.Child as CmdControl;
            //if (d != null)
            //{
            //    d.Visibility = Visibility.Visible;
            //    d.Cmd = obj;
            //}
        }

        private void TreeViewItem_PreviewMouseDown(object sender, MouseButtonEventArgs e)
        {

        }

        private void Tree_MouseDown(object sender, MouseButtonEventArgs e)
        {

        }

        private void tv1_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {

        }

        private void Tree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {

        }
    

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmMainsilan>(bd, sender);
        }

        private void btnMonitor_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmMonitor>(bd, sender);
        }

        private void btnSetting_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmSetting>(bd, sender);
        }

        private void btnUser_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmUserCtr>(bd, sender);
        }

        private void btnHistory_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmHistory>(bd, sender);
        }

        private void btnsearch_MouseDown(object sender, MouseButtonEventArgs e)
        {
            //if (!string.IsNullOrEmpty(shelftxt.Text))
            //{
            //    if (GlobalData.gbshelfvm.ContainsKey(shelftxt.Text))
            //    {
            //        ShelfViewModel shelf = new ShelfViewModel();
            //        GlobalData.gbshelfvm.TryGetValue(shelftxt.Text, out shelf);
            //        color = shelf.ShelfColor;
            //        shelf.ShelfColor = 3;
            //    }
            //}
        }
        private int? color = 0;
        private void btnsearch_Click(object sender, RoutedEventArgs e)
        {

        }

        private void btnsearch_MouseMove(object sender, MouseEventArgs e)
        {
            //if (!string.IsNullOrEmpty(shelftxt.Text))
            //{
            //    if (GlobalData.gbshelfvm.ContainsKey(shelftxt.Text))
            //    {
            //        ShelfViewModel shelf = new ShelfViewModel();
            //        GlobalData.gbshelfvm.TryGetValue(shelftxt.Text, out shelf);
            //        shelf.ShelfColor = color;
            //    }
            //}
        }

        private void btnIO_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmIoMap>(bd, sender);
        }

        /// <summary>
        /// 初始化录制回放服务
        /// </summary>
        private void InitializeRecordReplayAsync()
        {
            Logger.Instance.InfoLog("开始初始化录制回放服务");
            try
            {
                // 订阅事件
                GlobalData.recordReplayService.RecordingStateChanged += OnRecordingStateChanged;
                GlobalData.recordReplayService.PlaybackStateChanged += OnPlaybackStateChanged;

                _recordReplayInitialized = true;
                Logger.Instance.InfoLog("录制回放服务初始化成功");

                // 显示热键配置信息
                var config = _configManager.Config;
                Logger.Instance.InfoLog($"热键配置:");
                Logger.Instance.InfoLog($"  开始录制: {_configManager.GetHotKeyDisplayText(config.HotKeys.StartRecording)}");
                Logger.Instance.InfoLog($"  停止录制: {_configManager.GetHotKeyDisplayText(config.HotKeys.StopRecording)}");
                Logger.Instance.InfoLog($"  开始回放: {_configManager.GetHotKeyDisplayText(config.HotKeys.StartPlayback)}");
                Logger.Instance.InfoLog($"  停止回放: {_configManager.GetHotKeyDisplayText(config.HotKeys.StopPlayback)}");
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"初始化录制回放服务时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 键盘事件处理
        /// </summary>
        private async void MainWindow_KeyDown(object sender, KeyEventArgs e)
        {
            // 添加调试日志
            var modifiers = Keyboard.Modifiers;
            Logger.Instance.InfoLog($"键盘事件触发: {e.Key}, 修饰键: {modifiers}, 录制回放已初始化: {_recordReplayInitialized}");

            if (!_recordReplayInitialized)
            {
                Logger.Instance.InfoLog("录制回放服务未初始化，忽略键盘事件");
                return;
            }

            try
            {
                var config = _configManager.Config;

                // 检查开始录制热键
                if (_configManager.IsHotKeyMatch(e, config.HotKeys.StartRecording))
                {
                    Logger.Instance.InfoLog($"检测到开始录制热键: {_configManager.GetHotKeyDisplayText(config.HotKeys.StartRecording)}");
                    await HandleStartRecording();
                    e.Handled = true;
                    return;
                }

                // 检查停止录制热键
                if (_configManager.IsHotKeyMatch(e, config.HotKeys.StopRecording))
                {
                    Logger.Instance.InfoLog($"检测到停止录制热键: {_configManager.GetHotKeyDisplayText(config.HotKeys.StopRecording)}");
                    await HandleStopRecording();
                    e.Handled = true;
                    return;
                }

                // 检查开始回放热键
                if (_configManager.IsHotKeyMatch(e, config.HotKeys.StartPlayback))
                {
                    Logger.Instance.InfoLog($"检测到开始回放热键: {_configManager.GetHotKeyDisplayText(config.HotKeys.StartPlayback)}");
                    await HandleStartPlayback();
                    e.Handled = true;
                    return;
                }

                // 检查停止回放热键
                if (_configManager.IsHotKeyMatch(e, config.HotKeys.StopPlayback))
                {
                    Logger.Instance.InfoLog($"检测到停止回放热键: {_configManager.GetHotKeyDisplayText(config.HotKeys.StopPlayback)}");
                    HandleStopPlayback();
                    e.Handled = true;
                    return;
                }

                // 调试热键：Ctrl+Shift+C 清除所有状态
                if (e.Key == Key.C && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control &&
                    (Keyboard.Modifiers & ModifierKeys.Shift) == ModifierKeys.Shift)
                {
                    Logger.Instance.InfoLog("检测到清除状态热键: Ctrl+Shift+C");
                    HandleClearAllStates();
                    e.Handled = true;
                    return;
                }

                // 调试热键：Ctrl+Shift+T 测试UI绑定
                if (e.Key == Key.T && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control &&
                    (Keyboard.Modifiers & ModifierKeys.Shift) == ModifierKeys.Shift)
                {
                    Logger.Instance.InfoLog("检测到测试UI绑定热键: Ctrl+Shift+T");
                    HandleTestUIBinding();
                    e.Handled = true;
                    return;
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"处理键盘事件时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理开始录制
        /// </summary>
        private async Task HandleStartRecording()
        {
            try
            {
                if (GlobalData.recordReplayService.IsRecording)
                {
                    Logger.Instance.InfoLog("录制已在进行中");
                    return;
                }

                if (GlobalData.recordReplayService.IsPlaying)
                {
                    Logger.Instance.InfoLog("回放正在进行中，无法开始录制");
                    return;
                }

                string description = $"N2Purge录制_{DateTime.Now:yyyy-MM-dd_HH-mm-ss}";
                bool success = await GlobalData.recordReplayService.StartRecordingAsync(description);

                if (success)
                {
                    Logger.Instance.InfoLog("开始录制");
                }
                else
                {
                    Logger.Instance.ErrorLog("开始录制失败");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"开始录制时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理停止录制
        /// </summary>
        private async Task HandleStopRecording()
        {
            try
            {
                if (!GlobalData.recordReplayService.IsRecording)
                {
                    Logger.Instance.InfoLog("当前没有进行录制");
                    return;
                }

                var filePath = await GlobalData.recordReplayService.StopRecordingAsync(true);
                if (!string.IsNullOrEmpty(filePath))
                {
                    Logger.Instance.InfoLog($"录制已停止并保存，文件: {filePath}");
                }
                else
                {
                    Logger.Instance.ErrorLog("停止录制失败");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"停止录制时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理开始回放
        /// </summary>
        private async Task HandleStartPlayback()
        {
            try
            {
                if (GlobalData.recordReplayService.IsPlaying)
                {
                    Logger.Instance.InfoLog("回放已在进行中");
                    return;
                }

                if (GlobalData.recordReplayService.IsRecording)
                {
                    Logger.Instance.InfoLog("录制正在进行中，无法开始回放");
                    return;
                }

                // 回放最新的录制文件
                bool success = await GlobalData.recordReplayService.StartPlaybackLatestAsync();

                if (success)
                {
                    Logger.Instance.InfoLog("开始回放最新录制");
                }
                else
                {
                    Logger.Instance.ErrorLog("开始回放失败，可能没有可用的录制文件");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"开始回放时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理停止回放
        /// </summary>
        private void HandleStopPlayback()
        {
            try
            {
                if (!GlobalData.recordReplayService.IsPlaying)
                {
                    Logger.Instance.InfoLog("当前没有进行回放");
                    return;
                }

                GlobalData.recordReplayService.StopPlayback();
                Logger.Instance.InfoLog("回放已停止");
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"停止回放时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理清除所有状态
        /// </summary>
        private void HandleClearAllStates()
        {
            try
            {
                Logger.Instance.InfoLog("开始清除所有传送带状态...");

                // 查找传送带主窗体
                var conveyorWindow = Application.Current.Windows.OfType<N2Purge.Conveyor.frmConvryorMain>().FirstOrDefault();
                if (conveyorWindow != null)
                {
                    conveyorWindow.ClearAllShelfStates();
                    Logger.Instance.InfoLog("传送带状态清除完成");
                }
                else
                {
                    Logger.Instance.InfoLog("未找到传送带主窗体，无法清除状态");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"清除状态时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 处理测试UI绑定
        /// </summary>
        private void HandleTestUIBinding()
        {
            try
            {
                Logger.Instance.InfoLog("开始测试UI绑定...");

                // 查找传送带主窗体
                var conveyorWindow = Application.Current.Windows.OfType<N2Purge.Conveyor.frmConvryorMain>().FirstOrDefault();
                if (conveyorWindow != null)
                {
                    conveyorWindow.TestUIBinding();
                    Logger.Instance.InfoLog("UI绑定测试完成");
                }
                else
                {
                    Logger.Instance.InfoLog("未找到传送带主窗体，无法测试UI绑定");
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"测试UI绑定时发生异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 录制状态改变事件处理
        /// </summary>
        private void OnRecordingStateChanged(object? sender, string message)
        {
            Dispatcher.Invoke(() =>
            {
                Logger.Instance.InfoLog($"录制状态改变: {message}");
            });
        }

        /// <summary>
        /// 回放状态改变事件处理
        /// </summary>
        private void OnPlaybackStateChanged(object? sender, string message)
        {
            Dispatcher.Invoke(() =>
            {
                Logger.Instance.InfoLog($"回放状态改变: {message}");
            });
        }

        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        private void Window_Closed(object sender, EventArgs e)
        {
            try
            {
                // 清理录制回放资源
                if (_recordReplayInitialized)
                {
                    GlobalData.recordReplayService?.Dispose();
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"清理录制回放资源时发生异常: {ex.Message}", ex);
            }

            Application.Current.Shutdown();
        }

        //private void Button_Click_1(object sender, RoutedEventArgs e)
        //{

        //}
    }
}