﻿using N2Purge.ViewModel;
using N2Purge;
using N2Purge.frmUserControl;
using N2Purge.silan;
using N2Purge.userControls;
using N2Purge.ViewModel;
using System.Reflection;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using DBEntity;
using System.Windows.Threading;
using Proj.WCF;
using PlcTools;
using N2Purge.Conveyor;

namespace N2Purge
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
          
            this.WindowState = WindowState.Maximized;
            //Thread.Sleep(1000);
          //  MoveCrane();
            CmdControl cmdControl = new CmdControl();
            cmdControl.Visibility= Visibility.Collapsed;
           // bdbtn.Child = cmdControl;
            GlobalData.ControlLeftClick += GlobalData_ShelfClick;
            FrmHelper.Show<frmUserCtr>(bd, sender);
            GlobalData.IsUserLogin += GlobalData_IsUserLogin;
            //  GlobalData.gbsignalhelper=new N2Purge.CommunicationHelper.SignalirHelper(GlobalData.ServerUrl);
            btn(false);
            WCFClient.Instance.Connect();
           
            GlobalData.gbPlcHelper.LoadPlcConfig(System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\PlcConfig.json"));
           // GlobalData.PlcConnect();
        }
      
        private void btn(bool enable)
        {
            btnmain.IsEnabled=enable;
            btnSetting.IsEnabled=enable;
            btnMonitor.IsEnabled=enable;
            btnHistory.IsEnabled=enable;
            btnIO.IsEnabled=enable;
        }
        //用户登录后逻辑
        private void GlobalData_IsUserLogin(bool obj)
        {
            if (obj)
            {
                btn(true);
                Button_Click(null, null);
            }
            else 
            {
                btn(false);
            }
        }

        private void GlobalData_ShelfClick(string obj)
        {
            //var d = bdbtn.Child as CmdControl;
            //if (d != null)
            //{
            //    d.Visibility = Visibility.Visible;
            //    d.Cmd = obj;
            //}
        }

        private void TreeViewItem_PreviewMouseDown(object sender, MouseButtonEventArgs e)
        {

        }

        private void Tree_MouseDown(object sender, MouseButtonEventArgs e)
        {

        }

        private void tv1_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {

        }

        private void Tree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {

        }
    

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmMainsilan>(bd, sender);
        }

        private void btnMonitor_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmMonitor>(bd, sender);
        }

        private void btnSetting_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmSetting>(bd, sender);
        }

        private void btnUser_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmUserCtr>(bd, sender);
        }

        private void btnHistory_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmHistory>(bd, sender);
        }

        private void Window_Closed(object sender, EventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void btnsearch_MouseDown(object sender, MouseButtonEventArgs e)
        {
            //if (!string.IsNullOrEmpty(shelftxt.Text))
            //{
            //    if (GlobalData.gbshelfvm.ContainsKey(shelftxt.Text))
            //    {
            //        ShelfViewModel shelf = new ShelfViewModel();
            //        GlobalData.gbshelfvm.TryGetValue(shelftxt.Text, out shelf);
            //        color = shelf.ShelfColor;
            //        shelf.ShelfColor = 3;
            //    }
            //}
        }
        private int? color = 0;
        private void btnsearch_Click(object sender, RoutedEventArgs e)
        {

        }

        private void btnsearch_MouseMove(object sender, MouseEventArgs e)
        {
            //if (!string.IsNullOrEmpty(shelftxt.Text))
            //{
            //    if (GlobalData.gbshelfvm.ContainsKey(shelftxt.Text))
            //    {
            //        ShelfViewModel shelf = new ShelfViewModel();
            //        GlobalData.gbshelfvm.TryGetValue(shelftxt.Text, out shelf);
            //        shelf.ShelfColor = color;
            //    }
            //}
        }

        private void btnIO_Click(object sender, RoutedEventArgs e)
        {
            FrmHelper.Show<frmIoMap>(bd, sender);
        }



        //private void Button_Click_1(object sender, RoutedEventArgs e)
        //{

        //}
    }
}