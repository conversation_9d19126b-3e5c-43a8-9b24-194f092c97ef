﻿using DBEntity;
using EmulatorClient;
using Google.Protobuf.WellKnownTypes;
using Microsoft.Extensions.Logging;
using N2Purge.CcnfigClass;
using N2Purge.frmUserControl;
using N2Purge.userControls;
using N2Purge.ViewModel;
using Proj.Log;
using Proj.WCF;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Packaging;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using static System.Net.Mime.MediaTypeNames;

namespace N2Purge.silan
{
    /// <summary>
    /// frmMain.xaml 的交互逻辑
    /// </summary>
    public partial class frmMainsilan : UserControl
    {
        private const string m_strCraneSign = "(<PERSON>)";
        private bool _isConfigLoaded = false;
        public frmMainsilan()
        {
            InitializeComponent();
        }
        //json文件读取shelf配置
        public void loadShelfConfig(string path, Grid grid, bool left)
        {
            try
            {
                string json = File.ReadAllText(path);
                var shelfConfigs = JsonSerializer.Deserialize<List<ShelfConfig>>(json);

                foreach (var config in shelfConfigs)
                {
                    Shelf shelf = new Shelf();
                    shelf.shelfDbClick += Shelf_ShelfleftDbClicked;
                    shelf.ShelfLeftClick += Shelf_ShelfLeftClick;
                    shelf.HorizontalAlignment = HorizontalAlignment.Stretch;
                    shelf.VerticalAlignment = VerticalAlignment.Stretch;
                    shelf.Margin = new Thickness(0, 6.5, 10, 0); // 设置控件的间隙
                    grid.Children.Add(shelf);
                    //实例化模型，绑定
                    ShelfViewModel viewModel = new ShelfViewModel
                    {
                        ShelfLocation = config.ShelfLocation,
                        ShelfColor = 0,
                        IsPurge = config.IsPurge
                    };
                    //鼠标放置port处显示的窗体的模型，现阶段不用
                    SingleShelfInfoViewModel singleShelfInfoViewModel = new SingleShelfInfoViewModel();
                    if (!GlobalData.gbshelfinfovm.ContainsKey(viewModel.ShelfLocation))
                    {
                        GlobalData.gbshelfinfovm.Add(viewModel.ShelfLocation, singleShelfInfoViewModel);
                    }
                    //模型绑定
                    shelf.DataContext = viewModel;
                    if (left)//Left和right就是主界面Shelf区域显示的左右，现在是上下
                    {
                        Grid.SetRow(shelf, config.Row);
                        Grid.SetColumn(shelf, config.Column);
                        if (!GlobalData.gbshelfvm.ContainsKey(viewModel.ShelfLocation))
                        {
                            GlobalData.gbshelfvm.Add(viewModel.ShelfLocation, viewModel);
                        }

                    }
                    else
                    {
                        Grid.SetRow(shelf, config.Row);
                        Grid.SetColumn(shelf, config.Column);

                        if (!GlobalData.gbshelfvm.ContainsKey(viewModel.ShelfLocation))
                        {
                            GlobalData.gbshelfvm.Add(viewModel.ShelfLocation, viewModel);
                        }
                    }
                    // 设置shelf所在的行和列

                }

            }
            catch (Exception ex)
            {

                throw;
            }

        }
        //json读取Port配置
        public void loadPortConfig(Grid gridleft, Grid gridright)
        {
            try
            {
                string path2 = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\portConfig.json");
                string json1 = File.ReadAllText(path2);
                var portConfigs = JsonSerializer.Deserialize<List<PortConfig>>(json1);
                foreach (var config in portConfigs)
                {
                    Port port = new Port();
                    port.HorizontalAlignment = HorizontalAlignment.Stretch;
                    port.VerticalAlignment = VerticalAlignment.Stretch;
                    gbPortViewModel cur = new gbPortViewModel();

                    if (config.IsPurge)
                    {

                        switch (config.PortLocation)
                        {
                            case "30101":
                                port.Margin = new Thickness(10, 10, 20, 10); // 设置控件的间隙
                                cur.Op.PortLocation = config.PortLocation;
                                cur.Bd.PortLocation = "3010101";
                                cur.Lp.PortLocation = "3010102";

                                GlobalData.gbPortViewModels["30101"] = cur;
                                //将整个Port的模型绑定，具体的LP、OP、BD的绑定，需要去Xaml里面看
                                port.DataContext = GlobalData.gbPortViewModels["30101"];
                                //port.bdlp.DataContext = GlobalData.gbPortViewModels["30101"].Lp;
                                //port.bdbd.DataContext = GlobalData.gbPortViewModels["30101"].Bd;
                                //port.bdop.DataContext = GlobalData.gbPortViewModels["30101"].Op;
                                //GlobalData.gbPortViewModels["30101"].Lp.PortColor = 1;
                                //GlobalData.gbPortViewModels["30101"].Op.PortColor = 2;
                                //GlobalData.gbPortViewModels["30101"].Bd.PortColor = 2;
                                break;
                            case "30102":
                                port.Margin = new Thickness(10, 10, 20, 10); // 设置控件的间隙
                                cur.Op.PortLocation = config.PortLocation;
                                cur.Bd.PortLocation = "3010201";
                                cur.Lp.PortLocation = "3010202";
                                GlobalData.gbPortViewModels["30102"] = cur;
                                port.DataContext = GlobalData.gbPortViewModels["30102"];
                                //port.bdlp.DataContext = GlobalData.gbPortViewModels["30102"].Lp;
                                //port.bdbd.DataContext = GlobalData.gbPortViewModels["30102"].Bd;
                                //port.bdop.DataContext = GlobalData.gbPortViewModels["30102"].Op;
                                break;
                            case "30103":
                                port.Margin = new Thickness(0, 10, 10, 10); // 设置控件的间隙
                                cur.Op.PortLocation = config.PortLocation;
                                cur.Bd.PortLocation = "3010301";
                                cur.Lp.PortLocation = "3010302";
                                GlobalData.gbPortViewModels["30103"] = cur;
                                port.DataContext = GlobalData.gbPortViewModels["30103"];
                                //port.bdlp.DataContext = GlobalData.gbPortViewModels["30103"].Lp;
                                //port.bdbd.DataContext = GlobalData.gbPortViewModels["30103"].Bd;
                                //port.bdop.DataContext = GlobalData.gbPortViewModels["30103"].Op;
                                break;
                            case "40101":
                                port.Margin = new Thickness(0, 10, 30, 10); // 设置控件的间隙
                                cur.Op.PortLocation = config.PortLocation;
                                cur.Bd.PortLocation = "4010101";
                                cur.Lp.PortLocation = "4010102";
                                GlobalData.gbPortViewModels["40101"] = cur;
                                port.DataContext = GlobalData.gbPortViewModels["40101"];
                                //port.bdlp.DataContext = GlobalData.gbPortViewModels["40101"].Lp;
                                //port.bdbd.DataContext = GlobalData.gbPortViewModels["40101"].Bd;
                                //port.bdop.DataContext = GlobalData.gbPortViewModels["40101"].Op;
                                break;
                            case "40102":
                                port.Margin = new Thickness(20, 10, 10, 10); // 设置控件的间隙
                                cur.Op.PortLocation = config.PortLocation;
                                cur.Bd.PortLocation = "4010201";
                                cur.Lp.PortLocation = "4010202";
                                GlobalData.gbPortViewModels["40102"] = cur;
                                port.DataContext = GlobalData.gbPortViewModels["40102"];
                                //port.bdlp.DataContext = GlobalData.gbPortViewModels["40102"].Lp;
                                //port.bdbd.DataContext = GlobalData.gbPortViewModels["40102"].Bd;
                                //port.bdop.DataContext = GlobalData.gbPortViewModels["40102"].Op;
                                break;
                            case "40103":
                                port.Margin = new Thickness(0, 10, 30, 10); // 设置控件的间隙
                                cur.Op.PortLocation = config.PortLocation;
                                cur.Bd.PortLocation = "4010301";
                                cur.Lp.PortLocation = "4010302";
                                GlobalData.gbPortViewModels["40103"] = cur;
                                port.DataContext = GlobalData.gbPortViewModels["40103"];
                                //port.bdlp.DataContext = GlobalData.gbPortViewModels["40103"].Lp;
                                //port.bdbd.DataContext = GlobalData.gbPortViewModels["40103"].Bd;
                                //port.bdop.DataContext = GlobalData.gbPortViewModels["40103"].Op;
                                break;
                            case "40104":
                                port.Margin = new Thickness(20, 10, 10, 10); // 设置控件的间隙
                                cur.Op.PortLocation = config.PortLocation;
                                cur.Bd.PortLocation = "4010401";
                                cur.Lp.PortLocation = "4010402";
                                GlobalData.gbPortViewModels["40104"] = cur;
                                port.DataContext = GlobalData.gbPortViewModels["40104"];
                                //port.bdlp.DataContext = GlobalData.gbPortViewModels["40104"].Lp;
                                //port.bdbd.DataContext = GlobalData.gbPortViewModels["40104"].Bd;
                                //port.bdop.DataContext = GlobalData.gbPortViewModels["40104"].Op;
                                break;
                        }

                        gridleft.Children.Add(port);

                        // 设置port所在的行、列及跨度

                    }
                    else
                    {
                        //MR 4
                        port.Margin = new Thickness(0, 10, 10, 10); // 设置控件的间隙
                        cur.Op.PortLocation = config.PortLocation;
                        cur.Bd.PortLocation = "3010401";
                        cur.Lp.PortLocation = "3010402";
                        GlobalData.gbPortViewModels["30104"] = cur;
                        port.DataContext = GlobalData.gbPortViewModels["30104"];
                        //port.bdlp.DataContext = GlobalData.gbPortViewModels["30104"].Lp;
                        //port.bdbd.DataContext = GlobalData.gbPortViewModels["30104"].Bd;
                        //port.bdop.DataContext = GlobalData.gbPortViewModels["30104"].Op;
                        gridright.Children.Add(port);
                    }
                    port.PortLeftClick += Port_ShelfClick;
                    port.PortLeftDbClick += Port_ShelfDbClick;
                    // port.PortRightClick += Port_ShelfClick;
                    Grid.SetRow(port, config.Row);
                    Grid.SetColumn(port, config.Column);
                    Grid.SetRowSpan(port, config.RowSpan);
                    Grid.SetColumnSpan(port, config.ColumnSpan);
                }
            }
            catch (Exception ex)
            {

            }


        }

        private void UserControl_Loaded(object sender, RoutedEventArgs e)
        {
            if (_isConfigLoaded) return;
            //  storageIno.DataContext = GlobalData.gbfrmInfoViewModel;
            string path = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\shelfConfig.json");
            var innerGrid = this.FindName("InnerGrid") as Grid;
            //loadShelfConfig(path, innerGrid, true);
            string path1 = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\RightshelfConfig.json");
            var rightGrid = this.FindName("RightGrid") as Grid;
            //  loadShelfConfig(path1, rightGrid, false);
            transgerdgv.ItemsSource = GlobalData.gbtransferViewModels;
            // storageIno.DataContext=GlobalData.gbfrmInfoViewModel;
            // loadPortConfig(innerGrid, rightGrid);
            // craneContainer.DataContext = GlobalData.gbcranevm;
            //  GlobalData.gbcranevm.ContainerWidth = craneContainer.ActualWidth;
            //  GlobalData.gbcraneforkvm.CranePosition = 500;
            GlobalData.RefreshShelfDt();
            GlobalData.RefreshTransferInfo();
            GlobalData.transfercount += GlobalData_transfercount;
            CpuIndicator.UsagePercentage = 30d;
            _isConfigLoaded = true;
        }
        //页面搬运统计
        //add判断个数增加还是减少
        private void GlobalData_transfercount(bool add, TransferViewModel vm)
        {
            this.Dispatcher.Invoke(new Action(() =>
            {
                if (add)
                {
                    GlobalData.gbtransferViewModels.Add(vm);
                }
                else
                {
                    GlobalData.gbtransferViewModels.Remove(vm);
                }
                GlobalData.gbtopScInfovm.CommandCnt = GlobalData.gbtransferViewModels.Count();
            }));

        }


        //Port左侧鼠标双击
        private void Port_ShelfDbClick(object? sender, PortViewModel e)
        {
            GlobalData.PortleftDbClick(e.PortLocation, e);
        }
        //Port左侧鼠标单击
        private void Shelf_ShelfLeftClick(object? sender, ShelfViewModel e)
        {
            GlobalData.ControlleftClick(e.ShelfLocation);
        }
        //Shelf左侧鼠标双击
        private void Shelf_ShelfleftDbClicked(object? sender, ShelfViewModel e)
        {
            GlobalData.ShelfleftDbClick(e.ShelfLocation, e);
        }
        //Shelf左侧鼠标单击
        private void Port_ShelfClick(object? sender, PortViewModel e)
        {
            GlobalData.ControlleftClick(e.PortLocation);
        }

        private void CraneContainer_SizeChanged(object sender, SizeChangedEventArgs e)
        {
            if (GlobalData.gbcranevm != null)
            {
                //GlobalData.gbcranevm.ContainerWidth = Math.Round(craneContainer.ActualWidth, 2)- crane.ActualWidth;
                // GlobalData.gbcranevm.CraneWidth = crane.ActualWidth;
            }
        }

        private async void btnExcute_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!GlobalData.dbHelper._userManagement.CheckOperationPermission(2))
                {
                    lblTransferResult.Text = "Insufficient permissions to perform this operation.";
                    return;
                }
                var selecttab = frmat.tb.SelectedItem as TabItem;
                if (selecttab != null)
                {
                    return;
                }
                if (selecttab?.Name == "transfer")
                {
                    
                    string source = GlobalData.frmActionViewModel.transferExcuteViewModel.SourceLocation;
                    string dest = GlobalData.frmActionViewModel.transferExcuteViewModel.DestLocation;
                    var bResult = FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.StartCarrier", $"{source},{dest}");
                    var SourceLocation = frmat.tb.FindName("SourceLocation") as ComboBox;
                    var DestLocation = frmat.tb.FindName("DestLocation") as ComboBox;
                    string text = $"transfer init:from {SourceLocation.Text} to {DestLocation.Text}  failed";

                    if (bResult)
                    {
                        lblTransferResult.Text = "Transfer Successfully.";
                        Logger.Instance.OperationLog($"Transfer Successfully: SOURCE: {SourceLocation?.Text}, DEST:  {DestLocation?.Text}");
                        text = $"transfer init:from {SourceLocation.Text} to {DestLocation.Text}  success";
                        Logger.Instance.EventLog($"[CommandID]:unKnow, [EQP Event] From={SourceLocation.Text} To={DestLocation.Text} , Event ID: {(int)EqpEvent.TransferInitiated}, Event Name: {EqpEvent.TransferInitiated.ToString()}, Event Text: {text}");
                    }
                    else
                    {
                        lblTransferResult.Text = "Transfer Failure.";
                        Logger.Instance.OperationLog($"Transfer Failure: SOURCE: {SourceLocation?.Text}, DEST:  {DestLocation?.Text}");

                        Logger.Instance.EventLog($"[CommandID]:unKnow, [EQP Event] From={SourceLocation.Text} To={DestLocation.Text}, Event ID: {(int)EqpEvent.TransferInitiated}, Event Name: {EqpEvent.TransferInitiated.ToString()}, Event Text: {text}");
                    }
                }
                if (selecttab?.Name == "install")
                {
                    var dt = selecttab.DataContext as InstallViewModel;
                    var carrierIdTextBox = frmat.tb.FindName("CassetteId") as TextBox;
                    if (carrierIdTextBox != null)
                    {
                        if (carrierIdTextBox.Text == "")
                        {
                            lblTransferResult.Text = "There is no CST in the Source Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.installViewModel.CassetteId = carrierIdTextBox.Text;
                    }
                    var SourceLocation = frmat.tb.FindName("InstallSourceLocation") as TextBox;
                    if (SourceLocation != null)
                    {
                        if (SourceLocation.Text == "")
                        {
                            lblTransferResult.Text = "Please input the Source Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.installViewModel.SourceLocation = SourceLocation.Text;
                    }
                    TextBox? InstallCstState = frmat.tb.FindName("InstallCstState") as TextBox;
                    if (InstallCstState != null)
                    {
                        if (InstallCstState.Text == "")
                        {
                            lblTransferResult.Text = "Please input the Dest Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.installViewModel.InstallCstState = InstallCstState.Text;
                    }
                    ComboBox? Priority = frmat.tb.FindName("InstallIdResult") as ComboBox;
                    if (Priority != null)
                    {
                        if (Priority.Text == "")
                        {
                            lblTransferResult.Text = "Please input the IDR Result.";
                            return;
                        }
                        GlobalData.frmActionViewModel.installViewModel.Priority = Priority.Text;
                    }
                    //检查Location
                    string strSourceLocation = SourceLocation.Text;
                    TpLocation sourceLoc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strSourceLocation);
                    if (sourceLoc == null)
                    {
                        lblTransferResult.Text = "Can not find the Source Location.";
                        return;
                    }
                    if (sourceLoc.Is_Occupied != 0)
                    {
                        lblTransferResult.Text = "Can not install, because the Location has CST.";
                        return;
                    }
                    //发送给后端
                    Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    dicParams.Add("CARRIERID", carrierIdTextBox.Text);
                    dicParams.Add("CARRIERLOC", sourceLoc.Address);
                    dicParams.Add("CARRIERSTATE", InstallCstState.Text);
                    dicParams.Add("IDRRESULT", Priority.Text);

                    string text = $"Add Carrier: {carrierIdTextBox.Text} at {sourceLoc.Address} strCarrierLoc fail";
                    var carrier = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByIdAsync(carrierIdTextBox.Text);
                    if (carrier == null)
                    {
                        // 新增加一个carrier
                        var addResult = await GlobalData.dbHelper.tpCarrierdb.CreateTpCarrierAsync(new TpCarrier()
                        {
                            Id = carrierIdTextBox.Text,
                            Location = sourceLoc.Address,
                            State = InstallCstState.Text
                        });
                        if (addResult < 1)
                        {
                            Logger.Instance.OperationLog($"Failed to add Carrier: {carrierIdTextBox.Text} at {sourceLoc.Address}");

                            lblTransferResult.Text = "Submit Install Failure.";
                            Logger.Instance.OperationLog("Submit Install Failure: " + "CARRIERID: " + carrierIdTextBox.Text + ", CARRIERLOC: " + sourceLoc.Address + ", CARRIERSTATE: " + InstallCstState.Text + ", IDRRESULT: " + Priority.Text);

                            Logger.Instance.EventLog($"[CommandID]:unKnow, [EQP Event] Unit: {carrierIdTextBox.Text}, Event ID: {(int)EqpEvent.CarrierInstallCompleted}, Event Name: {EqpEvent.CarrierInstallCompleted.ToString()}, Event Text: {text}");
                            return;
                        }

                        sourceLoc.Carrier_ID = carrierIdTextBox.Text;
                        sourceLoc.Is_Occupied = 1;
                        var updateResult = await GlobalData.dbHelper.tpLocationdb.UpdateTpLocationAsync(sourceLoc, sourceLoc.Address);
                        if (updateResult < 1)
                        {
                            Logger.Instance.OperationLog($"Failed to update Location: {sourceLoc.Address} with Carrier: {carrierIdTextBox.Text}");

                            lblTransferResult.Text = " Install Failure.";
                            Logger.Instance.OperationLog("Install Failure: " + "CARRIERID: " + carrierIdTextBox.Text + ", CARRIERLOC: " + sourceLoc.Address + ", CARRIERSTATE: " + InstallCstState.Text + ", IDRRESULT: " + Priority.Text);

                            Logger.Instance.EventLog($"[CommandID]:unKnow, [EQP Event] Unit: {carrierIdTextBox.Text}, Event ID: {(int)EqpEvent.CarrierInstallCompleted}, Event Name: {EqpEvent.CarrierInstallCompleted.ToString()}, Event Text: {text}");
                            return;
                        }

                        lblTransferResult.Text = "Install Successfully.";
                        Logger.Instance.OperationLog("Install Successfully: " + "CARRIERID: " + carrierIdTextBox.Text + ", CARRIERLOC: " + sourceLoc.Address + ", CARRIERSTATE: " + InstallCstState.Text + ", IDRRESULT: " + Priority.Text);

                        text = $"Add Carrier: {carrierIdTextBox.Text} at {sourceLoc.Address} strCarrierLoc success";
                        Logger.Instance.EventLog($"[CommandID]:unKnow, [EQP Event] Unit: {carrierIdTextBox.Text}, Event ID: {(int)EqpEvent.CarrierInstallCompleted}, Event Name: {EqpEvent.CarrierInstallCompleted.ToString()}, Event Text: {text}");
                    }
                    else
                    {
                        // 如果存在则记录日志,并退出
                        Logger.Instance.OperationLog($"Carrier {carrierIdTextBox.Text} already exists at {sourceLoc.Address}");
                        return;
                    }
                }
                if (selecttab?.Name == "remove")
                {
                    var dt = selecttab.DataContext as TransferExcuteViewModel;
                    var carrierIdTextBox = frmat.tb.FindName("RemoveCstID") as TextBox;
                    if (carrierIdTextBox != null)
                    {
                        if (carrierIdTextBox.Text == "")
                        {
                            lblTransferResult.Text = "There is no CST in the Source Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.removeViewModel.CstID = carrierIdTextBox.Text;
                    }
                    var SourceLocation = frmat.tb.FindName("RemoveLocation") as TextBox;
                    if (SourceLocation != null)
                    {
                        if (SourceLocation.Text == "")
                        {
                            lblTransferResult.Text = "Please input the Source Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.removeViewModel.Location = SourceLocation.Text;
                    }
                    //检查location
                    string strLocation = SourceLocation.Text;
                    if (strLocation.Contains(m_strCraneSign))
                    {
                        strLocation = strLocation.Replace(m_strCraneSign, "");
                    }
                    TpLocation sourceLoc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strLocation);
                    if (sourceLoc == null)
                    {
                        lblTransferResult.Text = "Can not find the Source Location.";
                        return;
                    }
                    if (sourceLoc.Is_Occupied == 0)
                    {
                        lblTransferResult.Text = "Can not remove, because the Location has not CST.";
                        return;
                    }
                    Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    dicParams.Add("CARRIERID", carrierIdTextBox.Text);
                    dicParams.Add("CARRIERLOC", sourceLoc.Address);

                    /*
                     * 模拟FrameServer处理
                     */
                    var dbLoc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByCarrierIdAsync(carrierIdTextBox.Text.Trim());
                    var dbCarrier = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByIdAsync(carrierIdTextBox.Text.Trim());
                    if (dbCarrier == null)
                    {
                        dbLoc = sourceLoc;
                    }
                    var ret = await GlobalData.dbHelper.tpCarrierdb.DeleteTpCarrierAsync(carrierIdTextBox.Text.Trim());
                    if(ret!=1)
                    {
                        lblTransferResult.Text = "Remove Failure.";
                        Logger.Instance.OperationLog("Remove Successfully: " + "CARRIERID: " + carrierIdTextBox.Text + ", CARRIERLOC: " + sourceLoc.Address);
                        return;
                    }

                    if (dbLoc != null)
                    {
                        dbLoc.Carrier_ID = "";
                        dbLoc.Is_Occupied = 0;
                    }
                    ret = await GlobalData.dbHelper.tpLocationdb.UpdateTpLocationAsync(dbLoc, dbLoc.Address);
                    if(ret != 1)
                    {

                        lblTransferResult.Text = "Remove Failure.";
                        Logger.Instance.OperationLog("Submit Remove Successfully: " + "CARRIERID: " + carrierIdTextBox.Text + ", CARRIERLOC: " + sourceLoc.Address);
                        return;
                    }

                    lblTransferResult.Text = "Remove Successfully.";
                    Logger.Instance.OperationLog("Remove Successfully: " + "CARRIERID: " + carrierIdTextBox.Text + ", CARRIERLOC: " + sourceLoc.Address);
                }
                if (selecttab?.Name == "scan")
                {
                    var dt = selecttab.DataContext as InstallViewModel;
                    var carrierIdTextBox = frmat.tb.FindName("ScanCassetteId") as TextBox;
                    if (carrierIdTextBox != null)
                    {
                        if (carrierIdTextBox.Text == "")
                        {
                            lblTransferResult.Text = "There is no CST in the Source Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.scanViewModel.CassetteId = carrierIdTextBox.Text;
                    }
                    TextBox? SourceLocation = frmat.tb.FindName("ScanSourceLocation") as TextBox;
                    if (SourceLocation != null)
                    {
                        if (SourceLocation.Text == "")
                        {
                            lblTransferResult.Text = "Please input the Source Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.scanViewModel.SourceLocation = SourceLocation.Text;
                    }
                    TextBox? ScanPriority = frmat.tb.FindName("ScanPriority") as TextBox;
                    if (ScanPriority != null)
                    {
                        if (ScanPriority.Text == "")
                        {
                            lblTransferResult.Text = "Please input the Priority.";
                            return;
                        }
                        int iPriority = 0;
                        if (Int32.TryParse(ScanPriority.Text, out iPriority) == false)
                        {
                            lblTransferResult.Text = "Please input the number for Priority.";
                            return;
                        }
                        GlobalData.frmActionViewModel.scanViewModel.Priority = string.IsNullOrEmpty(ScanPriority.Text) ? 99 : Convert.ToInt16(ScanPriority.Text);
                    }
                    //检查Location
                    string strSourceLocation = SourceLocation.Text;
                    TpLocation sourceLoc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strSourceLocation);
                    if (sourceLoc == null)
                    {
                        lblTransferResult.Text = "Can not find the Source Location.";
                        return;
                    }
                    if (sourceLoc.Is_Occupied != 0)
                    {
                        lblTransferResult.Text = "Can not install, because the Location has CST.";
                        return;
                    }
                    Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    dicParams.Add("CARRIERID", strSourceLocation);
                    dicParams.Add("CARRIERLOC", sourceLoc.Address);
                    dicParams.Add("PRIORITY", ScanPriority.Text);
                    object objResult = await WCFClient.Instance.SendMessage("Scan", dicParams);
                    bool bResult = true;
                    if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
                    {
                        bResult = false;
                    }
                    if (bResult)
                    {
                        lblTransferResult.Text = "Submit Scan Successfully.";
                        Logger.Instance.OperationLog("Submit Scan Successfully: " + "CARRIERID: " + carrierIdTextBox.Text + ", CARRIERLOC: "
                          + sourceLoc.Address + ", ScanPriority: " + ScanPriority.Text);
                    }
                    else
                    {
                        lblTransferResult.Text = "Submit Install Failure.";
                        Logger.Instance.OperationLog("Submit Scan Failure:" + "CARRIERID: " + carrierIdTextBox.Text + ", CARRIERLOC: "
                         + sourceLoc.Address + ", ScanPriority: " + ScanPriority.Text);

                    }
                }
                if (selecttab?.Name == "move")
                {
                    Dictionary<string, object> dicSCParams = new Dictionary<string, object>();
                    dicSCParams.Add("NAME", "SC State");
                    var strSCState = await WCFClient.Instance.SendMessage("GetState", dicSCParams);
                    if (strSCState.ToString() != "Auto")
                    {
                        lblTransferResult.Text = "Please switch SC State to Auto.";
                        return;
                    }
                    //MoveSourceLocation
                    //MovePriority
                    var dt = selecttab.DataContext as MoveViewModel;
                    var MoveSourceLocation = frmat.tb.FindName("MoveSourceLocation") as ComboBox;
                    if (MoveSourceLocation != null)
                    {
                        if (MoveSourceLocation.Text == "")
                        {
                            lblTransferResult.Text = "There is no CST in the Source Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.moveViewModel.SourceLocation = MoveSourceLocation.Text;
                    }
                    var MovePriority = frmat.tb.FindName("MovePriority") as TextBox;
                    if (MovePriority != null)
                    {
                        if (MovePriority.Text == "")
                        {
                            lblTransferResult.Text = "Please input the Source Location.";
                            return;
                        }
                        GlobalData.frmActionViewModel.moveViewModel.Priority = MovePriority.Text;
                    }
                    //检查源目标
                    string strLocation = MoveSourceLocation.Text;
                    if (strLocation.Contains(m_strCraneSign))
                    {
                        strLocation = strLocation.Replace(m_strCraneSign, "");
                    }
                    TpLocation sourceLoc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strLocation);
                    if (sourceLoc == null)
                    {
                        lblTransferResult.Text = "Can not find the Source Location.";
                        return;
                    }
                    Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    dicParams.Add("SOURCE", sourceLoc.Address);
                    dicParams.Add("PRIORITY", MovePriority.Text);
                    object objResult = await WCFClient.Instance.SendMessage("Move", dicParams);
                    bool bResult = true;
                    if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
                    {
                        bResult = false;
                    }
                    if (bResult)
                    {
                        lblTransferResult.Text = "Submit Move Successfully.";
                        Logger.Instance.OperationLog("Submit Move Successfully: " + "MoveSourceLocation: " + MoveSourceLocation.Text
                        + ", MovePriority: " + sourceLoc.Address);
                    }
                    else
                    {
                        lblTransferResult.Text = "Submit Move Failure.";
                        Logger.Instance.OperationLog("Submit Move Failure: " + "MoveSourceLocation: " + MoveSourceLocation.Text
                         + ", MovePriority: " + sourceLoc.Address);
                    }
                }
            }
            catch (Exception ex)
            {
                throw;
            }
            //判断权限
        }

        private void ContextMenu_ContextMenuOpening(object sender, ContextMenuEventArgs e)
        {
            try
            {
                var dataGrid = sender as DataGrid;
                if (dataGrid == null) return;

                var contextMenu = dataGrid.ContextMenu;
                if (contextMenu == null) return;


                var selectedItem = transgerdgv.SelectedItem as TransferViewModel;
                if (selectedItem == null)
                {
                    e.Handled = true;  // 如果没有选中项，阻止菜单打开
                    return;
                }
                foreach (MenuItem menuItem in contextMenu.Items)
                {
                    switch (menuItem.Header.ToString())
                    {
                        case "Cancel":
                            menuItem.IsEnabled = selectedItem.State != "Transferring";
                            break;
                        case "Abort":
                            menuItem.IsEnabled = selectedItem.State == "Transferring";
                            break;
                        case "Update Priority":
                            menuItem.IsEnabled = selectedItem.State != "Transferring";
                            break;
                    }
                }
            }
            catch (Exception ex)
            {

                throw;
            }

        }

        private async void btncancel_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            if (menuItem?.Tag != null)
            {
                var selectedItem = menuItem.Tag as TransferViewModel;
                if (selectedItem != null)
                {
                    // 这里可以处理选中行的数据
                    // selectedItem 包含了选中行的所有信息
                    //Logger.Instance.OperationLog("Click button: cancel.");

                    Dictionary<string, object> dicParams = new Dictionary<string, object>();

                    dicParams.Add("COMMANDID", selectedItem.Id);
                    string strLog = "Click button: cancel, CommondID=";
                    strLog += selectedItem.Id.ToString();

                    //Logger.Instance.OperationLog(strLog);
                    object objResult = await WCFClient.Instance.SendMessage("Cancel", dicParams);
                    bool bResult = true;
                    if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
                    {
                        bResult = false;
                    }
                    if (bResult)
                    {
                        MessageBox.Show("Submit cancel successfully");
                    }
                    else
                    {
                        MessageBox.Show("Submit cancel failure");
                    }

                }
                else
                {
                    MessageBox.Show("Please select a transfer task.");
                    return;
                }
            }
        }

        private async void btnAbort_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            if (menuItem?.Tag != null)
            {
                var selectedItem = menuItem.Tag as TransferViewModel;
                if (selectedItem != null)
                {
                    // 这里可以处理选中行的数据
                    // selectedItem 包含了选中行的所有信息
                    Dictionary<string, object> dicCheck = new Dictionary<string, object>();
                    object objCheckAbort = await WCFClient.Instance.SendMessage("CheckAbort", dicCheck);
                    bool bCheckResult = true;
                    var d = objCheckAbort.ToString();
                    if (objCheckAbort == null || !bool.TryParse(objCheckAbort.ToString(), out bCheckResult))
                    {
                        bCheckResult = false;
                    }
                    if (!bCheckResult)
                    {
                        string strMsg = "PLC任务无法停止，请用示教盒操作，将Crane的Y轴缩回到原位。";
                        MessageBox.Show(strMsg);
                        Logger.Instance.OperationLog(strMsg);
                        return;
                    }

                    Dictionary<string, object> dicParams = new Dictionary<string, object>();
                    dicParams.Add("COMMANDID", selectedItem.Id);
                    object objResult = await WCFClient.Instance.SendMessage("Abort", dicParams);
                    bool bResult = true;
                    if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
                    {
                        bResult = false;
                    }
                    if (bResult)
                    {
                        MessageBox.Show("Submit abort successfully");
                    }
                    else
                    {
                        MessageBox.Show("Submit abort failure");
                    }
                }
                else
                {
                    MessageBox.Show("Please select a transfer task.");
                    return;
                }
            }
        }

        private async void btnUpdate_Click(object sender, RoutedEventArgs e)
        {
            var menuItem = sender as MenuItem;
            if (menuItem?.Tag != null)
            {
                var selectedItem = menuItem.Tag as TransferViewModel;
                if (selectedItem != null)
                {
                    string strCmdID = selectedItem.Id.ToString();
                    FrmUpdatePriority frmPriority = new FrmUpdatePriority(strCmdID);
                    frmPriority.CommandId = strCmdID;
                    if (frmPriority.ShowDialog() == true)
                    {
                        Dictionary<string, object> dicParams = new Dictionary<string, object>();
                        dicParams.Add("COMMANDID", strCmdID);
                        dicParams.Add("PRIORITY", frmPriority.Priority);
                        object objResult = await WCFClient.Instance.SendMessage("UpdatePriority", dicParams);
                        bool bResult = true;
                        if (objResult == null || !bool.TryParse(objResult.ToString(), out bResult))
                        {
                            bResult = false;
                        }
                        if (bResult)
                        {
                            MessageBox.Show("Submit update priority successfully");
                        }
                        else
                        {
                            MessageBox.Show("Submit update priority failure");
                        }
                    }
                }
            }
        }


    }
}
