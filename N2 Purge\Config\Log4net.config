﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <log4net>
    <!-- AlarmLog -->
    <logger name="AlarmLog">
      <level value="ALL"/>
      <appender-ref ref="AlarmLogFileAppender" />
    </logger>
    <appender name="AlarmLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'Alarm'/'Alarm_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>
    
    <!-- ExceptionLog -->
    <logger name="ExceptionLog">
      <level value="ALL"/>
      <appender-ref ref="ExceptionLogFileAppender" />
    </logger>
    <appender name="ExceptionLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'Exception'/'Exception_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>

    <!-- EventLog -->
    <logger name="EventLog">
      <level value="ALL"/>
      <appender-ref ref="EventLogFileAppender" />
    </logger>
    <appender name="EventLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'Event'/'Event_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>

    <!-- HostLog -->
    <logger name="HostLog">
      <level value="ALL"/>
      <appender-ref ref="HostLogFileAppender" />
    </logger>
    <appender name="HostLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'Host'/'Host_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>

    <!-- OperationLog -->
    <logger name="OperationLog">
      <level value="ALL"/>
      <appender-ref ref="OperationLogFileAppender" />
    </logger>
    <appender name="OperationLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'Operation'/'Operation_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>

    <!-- PIOLog -->
    <logger name="PIOLog">
      <level value="ALL"/>
      <appender-ref ref="PIOLogFileAppender" />
    </logger>
    <appender name="PIOLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'PIO'/'PIO_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>

    <!-- PLCLog -->
    <logger name="PLCLog">
      <level value="ALL"/>
      <appender-ref ref="PLCLogFileAppender" />
    </logger>
    <appender name="PLCLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'PLC'/'PLC_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>

    <!-- SCLog -->
    <logger name="SCLog">
      <level value="ALL"/>
      <appender-ref ref="SCLogFileAppender" />
    </logger>
    <appender name="SCLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'SC'/'SC_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>

    <!-- TransferLog -->
    <logger name="TransferLog">
      <level value="ALL"/>
      <appender-ref ref="TransferLogFileAppender" />
    </logger>
    <appender name="TransferLogFileAppender" type="log4net.Appender.RollingFileAppender" >
      <Encoding value="UTF-8" />
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
      <param name="AppendToFile" value="true" />
      <param name="MaxSizeRollBackups" value="100" />
      <param name="maximumFileSize" value="500KB" />
      <param name="RollingStyle" value="Composite" />
      <param name="StaticLogFileName" value="false" />
      <param name="File" value="logs" />
      <param name="DatePattern" value="yyyy-MM-dd/'Transfer'/'Transfer_'yyyyMMddHH'.log'"  />
      <layout type="log4net.Layout.PatternLayout">
        <param name="ConversionPattern" value="%d - %m%n" />
      </layout>
    </appender>
  </log4net>
</configuration>
