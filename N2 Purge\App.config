﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
  <connectionStrings>
    <add name="StkCConnection"
         connectionString="Server=127.0.0.1;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True"
         providerName="MySql.Data.MySqlClient" />
  </connectionStrings>
	<appSettings>
		<!--CV利用率刷新时间 毫秒-->
		<add key="ShelfRefreshInterval" value="1000" />
		<!--警告检测间隔时间 毫秒-->
		<add key="WarningRefreshInterval" value="500" />
		<!--每个点位处理间隔时间 毫秒-->
		<add key="PointLoopInterval" value="50" />
		<!--每个点位最大阻塞时间,超过时间系统将报警 毫秒,默认5分钟-->
		<add key="PointBlockTime" value="300000" />
	</appSettings>
</configuration>