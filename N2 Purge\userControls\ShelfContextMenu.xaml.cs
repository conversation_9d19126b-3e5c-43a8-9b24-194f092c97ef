using System.Windows.Controls;
using System.Windows;
using N2Purge.ViewModel;
using DBEntity;
using EmulatorClient;

namespace N2Purge.userControls
{
    public partial class ShelfContextMenu : ContextMenu
    {
        public ShelfContextMenu()
        {
            InitializeComponent();
            this.Visibility = Visibility.Visible;
            this.HorizontalAlignment = HorizontalAlignment.Left;
            this.VerticalAlignment = VerticalAlignment.Top;
            this.Opened += ShelfContextMenu_Opened;
        }

        private void ShelfContextMenu_Opened(object sender, RoutedEventArgs e)
        {
            // 当菜单打开时，检查是否应该显示"Set Exit"菜单项
            var currentVm = this.DataContext as ShelfViewModel;
            if (currentVm != null)
            {
                bool shouldShowSetExit = false;

                // 情况1：当前Shelf被选中且有货物（原有逻辑）
                if (currentVm.IsSelected && !string.IsNullOrEmpty(currentVm.FoupID))
                {
                    shouldShowSetExit = true;
                }
                // 情况2：当前Shelf是出口点位，且有其他选中的含货物Shelf
                else if (currentVm.IsExit)
                {
                    // 检查是否有其他被选中且含有货物的Shelf
                    shouldShowSetExit = HasSelectedCarrierShelf();
                }

                menuSetExit.Visibility = shouldShowSetExit ? Visibility.Visible : Visibility.Collapsed;
            }
            else
            {
                menuSetExit.Visibility = Visibility.Collapsed;
            }
            // 仅仅是入口点位或是出口点位时才显示ChangePortState菜单项
            if (currentVm!=null && (currentVm.IsEntry || currentVm.IsExit))
            {
                menuChangePortState.Visibility = Visibility.Visible;
            }
            else
            {
                menuChangePortState.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 检查是否有被选中且含有货物的Shelf
        /// </summary>
        private bool HasSelectedCarrierShelf()
        {
            try
            {
                // 通过静态实例访问frmConvryorMain
                var conveyorMain = N2Purge.Conveyor.frmConvryorMain.Instance;
                if (conveyorMain != null)
                {
                    return conveyorMain.HasSelectedCarrierShelf();
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从ShelfViewModel获取对应的point_id
        /// </summary>
        private int GetPointIdFromShelf(ShelfViewModel shelfVm)
        {
            try
            {
                var conveyorMain = N2Purge.Conveyor.frmConvryorMain.Instance;
                if (conveyorMain != null)
                {
                    return conveyorMain.GetPointIdFromShelfLocation(shelfVm.ShelfLocation);
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }
        private event EventHandler<ShelfViewModel> ShelfViewModelChanged;
        private void ShelfInfo_Click(object sender, RoutedEventArgs e)
        {
            // 处理Shelf Info点击事件
            var vm = this.DataContext as ShelfViewModel;
            if (vm != null)
            {
                GlobalData.ShelfMouseRightClick("ShelfInfo", vm);
            }
        }

        private void TransferCarrier_Click(object sender, RoutedEventArgs e)
        {
            // 处理Transfer Carrier点击事件
            var vm = this.DataContext as ShelfViewModel;
            if (vm != null)
            {
                GlobalData.ShelfMouseRightClick("Transfer", vm);
            }
        }

        private void InstallCarrier_Click(object sender, RoutedEventArgs e)
        {
            // 处理Install Carrier点击事件
            var vm = this.DataContext as ShelfViewModel;
            if (vm != null)
            {
                GlobalData.ShelfMouseRightClick("Install", vm);
            }
        }

        private void RemoveCarrier_Click(object sender, RoutedEventArgs e)
        {
            // 处理Remove Carrier点击事件
            var vm = this.DataContext as ShelfViewModel;
            if (vm != null)
            {
                GlobalData.ShelfMouseRightClick("Remove", vm);
            }
        }

        private void Scan_Click(object sender, RoutedEventArgs e)
        {
            // 处理Scan点击事件
            var vm = this.DataContext as ShelfViewModel;
            if (vm != null)
            {
                //GlobalData.ShelfMouseRightClick("Scan", vm);
                GlobalData.ShelfMouseRightClick("ChangeExit", vm);
            }
        }

        private async void Enable_Click(object sender, RoutedEventArgs e)
        {
            // 处理Enable点击事件
            var vm = this.DataContext as ShelfViewModel;
            if (vm != null)
            {
                //vm.IsEnabled = true;
                //vm.ShelfColor = 0;

                if (string.IsNullOrEmpty(vm.shelfName))
                {
                    return;
                }
                var shelfName = vm.shelfName.Replace("S", "");
                string msgName = "SetLocationEnabled";
                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                dicParams.Add("LOCATION", shelfName);
                dicParams.Add("ENABLED", 1);

                object obj = await Proj.WCF.WCFClient.Instance.SendMessage(msgName, dicParams);
                bool res = true;
                if (obj == null || !bool.TryParse(obj.ToString(), out res))
                {
                    res = false;
                }

                if (res)
                {
                    MessageBox.Show("Submit successfully!");
                }
                else
                {
                    MessageBox.Show("Submit failure!");
                }
            }
        }

        private async void Disable_Click(object sender, RoutedEventArgs e)
        {
            // 处理Disable点击事件
            var vm = this.DataContext as ShelfViewModel;
            if (vm != null)
            {
                //vm.IsEnabled = false;
                // vm.ShelfColor = 3;
                if (string.IsNullOrEmpty(vm.shelfName))
                {
                    return;
                }
                var shelfName = vm.shelfName.Replace("S", "");
                string msgName = "SetLocationEnabled";
                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                dicParams.Add("LOCATION", shelfName);
                dicParams.Add("ENABLED", 0);

                object obj = await Proj.WCF.WCFClient.Instance.SendMessage(msgName, dicParams);
                bool res = true;
                if (obj == null || !bool.TryParse(obj.ToString(), out res))
                {
                    res = false;
                }

                if (res)
                {
                    MessageBox.Show("Submit successfully!");
                }
                else
                {
                    MessageBox.Show("Submit failure!");
                }

            }
        }

        private async void ClearReserved_Click(object sender, RoutedEventArgs e)
        {
            // 处理Clear Reserved点击事件
            try
            {
                var vm = this.DataContext as ShelfViewModel;
                var strLocation = vm.ShelfLocation;
                if (string.IsNullOrEmpty(strLocation))
                {
                    return;
                }
                TpLocation loc = await GlobalData.dbHelper.tpLocationdb.GetTpLocationByAddressAsync(strLocation);
                if (loc == null)
                {
                    MessageBox.Show("Can not find the location.");
                    return;
                }
                string msgName = "ClearReserved";
                Dictionary<string, object> dicParams = new Dictionary<string, object>();
                dicParams.Add("LOCATION", loc.Address);

                object obj = Proj.WCF.WCFClient.Instance.SendMessage(msgName, dicParams);
                bool res = true;
                if (obj == null || !bool.TryParse(obj.ToString(), out res))
                {
                    res = false;
                }

                if (res)
                {
                    //MessageBox.Show("Submit successfully!");
                }
                else
                {
                    MessageBox.Show("Submit failure!");
                }

            }
            catch (Exception ex)
            {

            }
        }

        private void SetExit_Click(object sender, RoutedEventArgs e)
        {
            // 处理Set Exit点击事件
            var currentVm = this.DataContext as ShelfViewModel;
            if (currentVm != null)
            {
                string foupID = string.Empty;
                string currentLocation = string.Empty;
                int targetPointID = 0;

                if (currentVm.IsExit)
                {
                    try
                    {
                        var conveyorMain = N2Purge.Conveyor.frmConvryorMain.Instance;
                        if (conveyorMain != null && conveyorMain.HasSelectedCarrierShelf())
                        {
                            var (selectedFoupID, selectedLocation) = conveyorMain.GetSelectedCarrierInfo();
                            if (!string.IsNullOrEmpty(selectedFoupID))
                            {
                                // 获取当前右键出口点位的point_id
                                targetPointID = GetPointIdFromShelf(currentVm);

                                if (targetPointID > 0)
                                {
                                    // 调用FinClientHelper写入标签值
                                    FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.foupid", selectedFoupID);
                                    FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.newpoint", targetPointID.ToString());

                                    //var foupid = FinClientHelper.GetIOValue("CARRIER_EMULATOR.Command.foupid");
                                    //var newpoint = FinClientHelper.GetIOValue("CARRIER_EMULATOR.Command.newpoint");
                                    //MessageBox.Show($"已成功设置货物 {selectedFoupID} 的新出口为点位 {targetPointID} ({currentVm.ShelfLocation}),newpoint={newpoint},foupid={foupid}","操作成功", MessageBoxButton.OK, MessageBoxImage.Information);

                                    // 记录日志
                                    Proj.Log.Logger.Instance.OperationLog($"设置货物出口: FoupID={selectedFoupID}, 新出口点位={targetPointID}, 地址={currentVm.ShelfLocation}, 原位置={selectedLocation}");
                                }
                                else
                                {
                                    MessageBox.Show("无法获取出口点位ID", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                                }
                            }
                        }
                        else
                        {
                            MessageBox.Show("请先选择一个含有货物的Shelf", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"设置出口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        Proj.Log.Logger.Instance.ExceptionLog($"设置出口异常: {ex.Message}\n{ex.StackTrace}");
                    }
                }
            }
        }

        private void Comment_Click(object sender, RoutedEventArgs e)
        {
            // 处理Comment点击事件
        }

        private void ClearCarrier_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var vm = this.DataContext as ShelfViewModel;
                if (vm == null || string.IsNullOrEmpty(vm.ShelfLocation))
                {
                    MessageBox.Show("无法获取Shelf信息", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // 确认对话框
                var result = MessageBox.Show($"确定要清除位置 {vm.ShelfLocation} 的货物显示吗？\n\n注意：这只会清除界面显示，不会影响实际的PLC数据。",
                                           "确认清除", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // 获取对应的点位ID
                    var conveyorMain = N2Purge.Conveyor.frmConvryorMain.Instance;
                    if (conveyorMain != null)
                    {
                        var pointId = conveyorMain.GetPointIdFromShelfLocation(vm.ShelfLocation);
                        if (pointId > 0)
                        {
                            // 强制更新Shelf显示为无货状态
                            conveyorMain.UpdateShelfByPointID(pointId.ToString(), 0, null);

                            MessageBox.Show($"已清除位置 {vm.ShelfLocation} 的货物显示", "操作成功",
                                          MessageBoxButton.OK, MessageBoxImage.Information);

                            // 记录日志
                            Proj.Log.Logger.Instance.OperationLog($"手动清除货物显示: 位置={vm.ShelfLocation}, 点位ID={pointId}");
                        }
                        else
                        {
                            MessageBox.Show("无法找到对应的点位ID", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else
                    {
                        MessageBox.Show("无法获取Conveyor实例", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清除货物失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Proj.Log.Logger.Instance.ExceptionLog($"清除货物异常: {ex.Message}\n{ex.StackTrace}");
            }
        }

        /// <summary>
        /// 切换入口/出口
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ChangePortState_Click(object sender, RoutedEventArgs e)
        {
            // 处理Set Exit点击事件
            var currentVm = this.DataContext as ShelfViewModel;
            if (currentVm != null)
            {
                string foupID = string.Empty;
                string currentLocation = string.Empty;
                int targetPointID = 0;
                var conveyorMain = N2Purge.Conveyor.frmConvryorMain.Instance;
                try
                {
                    // 获取当前右键出口点位的point_id
                    targetPointID = GetPointIdFromShelf(currentVm);

                    if (currentVm.IsExit)// 当前是出口点位
                    {
                        // 调用FinClientHelper写入标签值
                        FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.ChangePortState", $"{targetPointID},Enter");
                        // 记录日志
                        Proj.Log.Logger.Instance.OperationLog($"设置货物入口: {targetPointID}=> 入口");
                    }
                    else if (currentVm.IsEntry)// 当前是入口点位
                    {
                        // 调用FinClientHelper写入标签值
                        FinClientHelper.WriteTagValue("CARRIER_EMULATOR.Command.ChangePortState", $"{targetPointID},Exit");
                        // 记录日志
                        Proj.Log.Logger.Instance.OperationLog($"设置货物出口: {targetPointID}=> 出口");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"切换入口失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                    Proj.Log.Logger.Instance.ExceptionLog($"切换入口失败: {ex.Message}\n{ex.StackTrace}");
                }
            }
        }
    }
}