using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using System.Windows.Forms;
using Proj.Log;
using Timer = System.Windows.Forms.Timer;

namespace N2Purge.Services
{
    /// <summary>
    /// 简化的录制回放服务
    /// </summary>
    public class SimpleRecordReplayService
    {
        #region 字段和属性

        private bool _isRecording = false;
        private bool _isPlaying = false;
        private List<InputAction> _recordedActions = new List<InputAction>();
        private DateTime _recordingStartTime;
        private string _recordingDirectory;
        private Timer _playbackTimer;
        private int _playbackIndex = 0;
        private List<InputAction> _playbackActions = new List<InputAction>();

        /// <summary>
        /// 是否正在录制
        /// </summary>
        public bool IsRecording => _isRecording;

        /// <summary>
        /// 是否正在回放
        /// </summary>
        public bool IsPlaying => _isPlaying;

        #endregion

        #region 事件

        /// <summary>
        /// 录制状态改变事件
        /// </summary>
        public event EventHandler<string> RecordingStateChanged;

        /// <summary>
        /// 回放状态改变事件
        /// </summary>
        public event EventHandler<string> PlaybackStateChanged;

        #endregion

        #region 构造函数

        public SimpleRecordReplayService()
        {
            _recordingDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "RecordReplay");
            if (!Directory.Exists(_recordingDirectory))
            {
                Directory.CreateDirectory(_recordingDirectory);
            }

            _playbackTimer = new Timer();
            _playbackTimer.Tick += PlaybackTimer_Tick;
        }

        #endregion

        #region 录制功能

        /// <summary>
        /// 开始录制
        /// </summary>
        public async Task<bool> StartRecordingAsync(string description = "")
        {
            try
            {
                if (_isRecording || _isPlaying)
                {
                    return false;
                }

                _isRecording = true;
                _recordedActions.Clear();
                _recordingStartTime = DateTime.Now;

                // 安装钩子（这里简化处理，实际应该使用低级钩子）
                Application.AddMessageFilter(new InputMessageFilter(this));

                RecordingStateChanged?.Invoke(this, "开始录制");
                Logger.Instance.InfoLog($"开始录制: {description}");

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"开始录制失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止录制
        /// </summary>
        public async Task<string> StopRecordingAsync(bool autoSave = true)
        {
            try
            {
                if (!_isRecording)
                {
                    return null;
                }

                _isRecording = false;

                // 移除钩子
                // Application.RemoveMessageFilter(...); // 简化处理

                string filePath = null;
                if (autoSave && _recordedActions.Count > 0)
                {
                    filePath = await SaveRecordingAsync();
                }

                RecordingStateChanged?.Invoke(this, "停止录制");
                Logger.Instance.InfoLog($"停止录制，共录制 {_recordedActions.Count} 个操作");

                return filePath;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"停止录制失败: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 保存录制
        /// </summary>
        private async Task<string> SaveRecordingAsync()
        {
            try
            {
                string fileName = $"Recording_{DateTime.Now:yyyyMMdd_HHmmss}.json";
                string filePath = Path.Combine(_recordingDirectory, fileName);

                var recordingData = new RecordingData
                {
                    StartTime = _recordingStartTime,
                    EndTime = DateTime.Now,
                    Actions = _recordedActions
                };

                string json = JsonSerializer.Serialize(recordingData, new JsonSerializerOptions { WriteIndented = true });
                await File.WriteAllTextAsync(filePath, json);

                Logger.Instance.InfoLog($"录制已保存到: {filePath}");
                return filePath;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"保存录制失败: {ex.Message}", ex);
                return null;
            }
        }

        #endregion

        #region 回放功能

        /// <summary>
        /// 开始回放最新录制
        /// </summary>
        public async Task<bool> StartPlaybackLatestAsync()
        {
            try
            {
                var files = Directory.GetFiles(_recordingDirectory, "Recording_*.json");
                if (files.Length == 0)
                {
                    Logger.Instance.InfoLog("没有找到录制文件");
                    return false;
                }

                // 获取最新文件
                Array.Sort(files);
                string latestFile = files[files.Length - 1];

                return await StartPlaybackFromFileAsync(latestFile);
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"开始回放最新录制失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 从文件开始回放
        /// </summary>
        public async Task<bool> StartPlaybackFromFileAsync(string filePath)
        {
            try
            {
                if (_isRecording || _isPlaying)
                {
                    return false;
                }

                if (!File.Exists(filePath))
                {
                    Logger.Instance.ErrorLog($"录制文件不存在: {filePath}");
                    return false;
                }

                string json = await File.ReadAllTextAsync(filePath);
                var recordingData = JsonSerializer.Deserialize<RecordingData>(json);

                if (recordingData?.Actions == null || recordingData.Actions.Count == 0)
                {
                    Logger.Instance.InfoLog("录制文件中没有操作数据");
                    return false;
                }

                _playbackActions = recordingData.Actions;
                _playbackIndex = 0;
                _isPlaying = true;

                PlaybackStateChanged?.Invoke(this, "开始回放");
                Logger.Instance.InfoLog($"开始回放: {filePath}, 共 {_playbackActions.Count} 个操作");

                // 开始回放定时器
                _playbackTimer.Interval = 50; // 50ms间隔
                _playbackTimer.Start();

                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"开始回放失败: {ex.Message}", ex);
                return false;
            }
        }

        /// <summary>
        /// 停止回放
        /// </summary>
        public void StopPlayback()
        {
            try
            {
                if (!_isPlaying)
                {
                    return;
                }

                _isPlaying = false;
                _playbackTimer.Stop();

                PlaybackStateChanged?.Invoke(this, "停止回放");
                Logger.Instance.InfoLog("回放已停止");
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"停止回放失败: {ex.Message}", ex);
            }
        }

        #endregion

        #region 回放定时器

        private void PlaybackTimer_Tick(object sender, EventArgs e)
        {
            try
            {
                if (!_isPlaying || _playbackIndex >= _playbackActions.Count)
                {
                    StopPlayback();
                    return;
                }

                var action = _playbackActions[_playbackIndex];
                
                // 简化的回放逻辑（实际应该使用SendInput等API）
                Logger.Instance.InfoLog($"回放操作: {action.Type} at ({action.X}, {action.Y})");

                _playbackIndex++;
            }
            catch (Exception ex)
            {
                Logger.Instance.ExceptionLog($"回放操作失败: {ex.Message}", ex);
                StopPlayback();
            }
        }

        #endregion

        #region 内部类

        /// <summary>
        /// 输入操作
        /// </summary>
        public class InputAction
        {
            public string Type { get; set; } = "";
            public int X { get; set; }
            public int Y { get; set; }
            public DateTime Timestamp { get; set; }
            public string Data { get; set; } = "";
        }

        /// <summary>
        /// 录制数据
        /// </summary>
        public class RecordingData
        {
            public DateTime StartTime { get; set; }
            public DateTime EndTime { get; set; }
            public List<InputAction> Actions { get; set; } = new List<InputAction>();
        }

        /// <summary>
        /// 输入消息过滤器（简化版本）
        /// </summary>
        private class InputMessageFilter : IMessageFilter
        {
            private readonly SimpleRecordReplayService _service;

            public InputMessageFilter(SimpleRecordReplayService service)
            {
                _service = service;
            }

            public bool PreFilterMessage(ref Message m)
            {
                // 简化的消息处理
                if (_service._isRecording)
                {
                    // 记录鼠标和键盘事件
                    if (m.Msg >= 0x0200 && m.Msg <= 0x020E) // 鼠标消息
                    {
                        var action = new InputAction
                        {
                            Type = "Mouse",
                            X = m.LParam.ToInt32() & 0xFFFF,
                            Y = (m.LParam.ToInt32() >> 16) & 0xFFFF,
                            Timestamp = DateTime.Now,
                            Data = m.Msg.ToString()
                        };
                        _service._recordedActions.Add(action);
                    }
                }
                return false;
            }
        }

        #endregion

        #region 释放资源

        public void Dispose()
        {
            _playbackTimer?.Dispose();
        }

        #endregion
    }
}
