﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media;

namespace N2Purge.ViewModel
{
    public class ShelfViewModel:VmPropertyChange
    {
        public bool IsPurge = false;
        
        private bool _IsEnabled = true;
        public bool IsEnabled
        {
            get { return _IsEnabled; }
            set
            {
                if (_IsEnabled != value)
                {
                    _IsEnabled = value;
                    OnPropertyChanged(nameof(IsEnabled));
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }
        
        public string shelfName { get; set; } = string.Empty;
        
        private string? _ShelfLocation;
        public string? ShelfLocation
        {
            get { return _ShelfLocation; }
            set
            {
                if (_ShelfLocation != value)
                {
                    _ShelfLocation = value;
                    OnPropertyChanged(nameof(ShelfLocation));
                }
            }
        }
        
        private int? _ShelfColor;
        public int? ShelfColor
        {
            get { return _ShelfColor; }
            set
            {
                if (_ShelfColor != value)
                {
                    _ShelfColor = value;
                    OnPropertyChanged(nameof(ShelfColor));
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(ShelfColorBrush));
                    OnPropertyChanged(nameof(FoupDisplayText));
                }
            }
        }

        private string? _FoupID;
        public string? FoupID
        {
            get { return _FoupID; }
            set
            {
                if (_FoupID != value)
                {
                    _FoupID = value;
                    OnPropertyChanged(nameof(FoupID));
                    OnPropertyChanged(nameof(FoupDisplayText));
                    OnPropertyChanged(nameof(StatusText));
                }
            }
        }

        private bool _IsEntry;
        public bool IsEntry
        {
            get { return _IsEntry; }
            set
            {
                if (_IsEntry != value)
                {
                    _IsEntry = value;
                    OnPropertyChanged(nameof(IsEntry));
                    OnPropertyChanged(nameof(EntryExitText));
                    OnPropertyChanged(nameof(HasEntryExitMark));
                }
            }
        }

        private bool _IsExit;
        public bool IsExit
        {
            get { return _IsExit; }
            set
            {
                if (_IsExit != value)
                {
                    _IsExit = value;
                    OnPropertyChanged(nameof(IsExit));
                    OnPropertyChanged(nameof(EntryExitText));
                    OnPropertyChanged(nameof(HasEntryExitMark));
                }
            }
        }

        private bool _IsSelected;
        public bool IsSelected
        {
            get { return _IsSelected; }
            set
            {
                if (_IsSelected != value)
                {
                    _IsSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                    OnPropertyChanged(nameof(BorderBrush));
                    OnPropertyChanged(nameof(BorderThickness));
                }
            }
        }
        
        // 添加颜色画刷属性
        public Brush ShelfColorBrush
        {
            get
            {
                return ShelfColor switch
                {
                    0 => Brushes.Snow,
                    1 => Brushes.LightGreen,
                    2 => Brushes.Yellow,
                    3 => Brushes.Gray,
                    9 => Brushes.Orange,
                    _ => Brushes.White
                };
            }
        }
        
        // 状态文本属性
        public string StatusText
        {
            get
            {
                if (!IsEnabled) return "禁用";

                return ShelfColor switch
                {
                    0 => "空闲",
                    1 => "有货",
                    2 => "传送",
                    3 => "禁用",
                    9 => "警告",
                    _ => "未知"
                };
            }
        }

        // 货物ID显示文本属性
        public string FoupDisplayText
        {
            get
            {
                if (!string.IsNullOrEmpty(FoupID) && (ShelfColor == 1 || ShelfColor == 2 || ShelfColor == 9))
                {
                    // 有货物时显示货物ID，限制长度避免显示过长
                    return FoupID.Length > 8 ? FoupID.Substring(0, 8) + "..." : FoupID;
                }
                return string.Empty;
            }
        }

        // 入口出口标记文本属性
        public string EntryExitText
        {
            get
            {
                if (IsEntry && IsExit)
                    return "IN/OUT";
                else if (IsEntry)
                    return "IN";
                else if (IsExit)
                    return "OUT";
                return string.Empty;
            }
        }

        // 是否有入口出口标记
        public bool HasEntryExitMark
        {
            get
            {
                return IsEntry || IsExit;
            }
        }

        // 入口出口标记颜色
        public Brush EntryExitBrush
        {
            get
            {
                if (IsEntry && IsExit)
                    return Brushes.Purple;
                else if (IsEntry)
                    return Brushes.Blue;
                else if (IsExit)
                    return Brushes.Orange;
                return Brushes.Transparent;
            }
        }

        // 边框画刷属性
        public Brush BorderBrush
        {
            get
            {
                if (IsSelected)
                    return Brushes.Blue;
                return Brushes.Green; // 默认绿色边框
            }
        }

        // 边框厚度属性
        public int BorderThickness
        {
            get
            {
                if (IsSelected)
                    return 3; // 选中时边框更粗
                return 1; // 默认边框厚度
            }
        }
        
        // 添加颜色描述属性
        public string ColorDescription
        {
            get
            {
                return ShelfColor switch
                {
                    0 => "正常",
                    1 => "货物进入",
                    2 => "传送中",
                    3 => "已禁用",
                    9 => "超时警告",
                    _ => "未定义"
                };
            }
        }
        
        // 添加显示名称属性（如果需要显示更友好的名称）
        public string DisplayName
        {
            get
            {
                if (!string.IsNullOrEmpty(shelfName))
                    return shelfName;
                return ShelfLocation ?? "未知";
            }
        }

        // 公共方法用于强制刷新属性通知
        public void RefreshFoupDisplay()
        {
            OnPropertyChanged(nameof(FoupDisplayText));
        }

        // 公共方法用于强制刷新选择状态显示
        public void RefreshSelectionDisplay()
        {
            OnPropertyChanged(nameof(IsSelected));
            OnPropertyChanged(nameof(BorderBrush));
            OnPropertyChanged(nameof(BorderThickness));
        }
    }
}
