﻿using N2Purge.ViewModel;
using N2Purge.CommunicationHelper;
using N2Purge.UserfulClass;
using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DBEntity;
using System.Collections.ObjectModel;
using System.Windows.Threading;
using System.Diagnostics.Eventing.Reader;
using PlcTools;
using CSVTools;
using N2Purge.userControls;
using Proj.Log;
using System.IO;

namespace N2Purge
{
   public class GlobalData
    {
        #region 绑定模型
        public static SettingViewModel gbsettingvm = new SettingViewModel();//Setting 页面的viewmodel
        public static StatisticsViewModel gbstatisticsViewModel = new StatisticsViewModel();//统计报表 页面的viewmodel
        public static Dictionary<string, gbPortViewModel> gbPortViewModels = new Dictionary<string, gbPortViewModel>();//Port 的viewmodel
        public static Dictionary<string, IOStatusViewModel> gbCmdstatusVm = new Dictionary<string, IOStatusViewModel>();//IOMap Crane
        public static Dictionary<string, Dictionary<string, IOStatusViewModel>> gbPortCmdstatusVm = new Dictionary<string, Dictionary<string, IOStatusViewModel>>();//IOmap Port
        public static ObservableCollection<TransferViewModel> gbtransferViewModels = new ObservableCollection<TransferViewModel>();//主页搬送的dgv的vm
        public static FrmInfoViewModel gbfrmInfoViewModel { get; set; } = new FrmInfoViewModel();  //FrmInfo Vm
        public static Dictionary<string, ShelfViewModel> gbshelfvm = new Dictionary<string, ShelfViewModel>();//shelf数据绑定
        public static TopScInfoViewModel gbtopScInfovm = new TopScInfoViewModel();//Top 信息绑定模型
        public static CraneViewModel gbcranevm = new CraneViewModel();//crane vM
        public static Dictionary<string, PurgeShelfVm> gbpurgevm = new Dictionary<string, PurgeShelfVm>();//N2 shelf vm
        public static frmActionViewModel frmActionViewModel = new frmActionViewModel(); //frmAction页面绑定的数据
        public static Dictionary<string, SingleShelfInfoViewModel> gbshelfinfovm = new Dictionary<string, SingleShelfInfoViewModel>();
        // public static CraneForkViewModel gbcraneforkvm = new CraneForkViewModel();
  
        #endregion

        public static CsvHelper csvHelper=new CsvHelper();//CSV导出
        public static PlcHelper gbPlcHelper=new PlcHelper();//PLC读写
        public static DbHelper dbHelper = new DbHelper();//数据库
      
        
        public static event Action<bool> IsUserLogin;//用户是否登录
        public static event Action<bool, TransferViewModel> transfercount;
        public static event Action<string>? ControlLeftClick;//左键点击
        public static event Action<string, ShelfViewModel>? ShelfRightClick;//Shelf右键点击
        public static event Action<string, PortViewModel>? PortRightClick;//Port右键点击
        public static event Action<string, ShelfViewModel>? ShelfLeftDbClick;//Shelf左键双击
        public static event Action<string, PortViewModel>? PortLeftDbClick;//Port左键双击

        private static CancellationTokenSource _refreshTransferCts = new CancellationTokenSource();


        private static Dictionary<string, TpResult> previousOccupiedStates = new Dictionary<string, TpResult>();
        // private static Dictionary<string, int> previousOccupiedStates = new Dictionary<string, int>();
        //private static Dictionary<string, int> previousProhibitedStates = new Dictionary<string, int>();
        //private static Dictionary<string, int> previousReservedStates = new Dictionary<string, int>();
        //private static Dictionary<string, string> previousCarrierIds = new Dictionary<string, string>();



        // public static WebApiHelper gbwebapi = new WebApiHelper();
        //public static SignalirHelper? gbsignalhelper;
        //Plc连接
        public static async Task PlcConnect()
        {
            await gbPlcHelper.LoadPLCData();
            gbPlcHelper.LoadPlcConfig(Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Config\\PlcConfig.json"));
            gbPlcHelper.ConnectEquipment();
            GlobalData.gbcranevm.PlcPosition = Math.Round((double)GlobalData.gbPlcHelper.PLCRead("s2hMotorStatusX.CurrentPosition").data, 2);
            MoveCrane();
        }
        //控件左击
        public static void ControlleftClick(string str)
        {
            ControlLeftClick?.Invoke(str);
        }
        //Shelf左双击
        public static void ShelfleftDbClick(string str, ShelfViewModel viewModel)
        {
            ShelfLeftDbClick?.Invoke(str, viewModel);
        }
        //Shelf右击
        public static void ShelfMouseRightClick(string str, ShelfViewModel viewModel)
        {
            ShelfRightClick?.Invoke(str, viewModel);
        }
        //Port左双击
        public static void PortleftDbClick(string str, PortViewModel viewModel)
        {
            PortLeftDbClick?.Invoke(str, viewModel);
        }
        //Port右击
        public static void PortMouseRightClick(string str, PortViewModel viewModel)
        {
            PortRightClick?.Invoke(str, viewModel);
        }
        //用户登录
        public static void UserLogin(bool IslOGIN)
        {
            IsUserLogin?.Invoke(IslOGIN);
        }
    
        // 在某个方法中更新CranePosition
        public static void MoveCrane()
        {
            Task.Run(() => {
                while (true)
                {
                   
                    GlobalData.gbcranevm.PlcPosition =Math.Round( (double)gbPlcHelper.PLCRead("s2hMotorStatusX.CurrentPosition").data,2);
                    var  t= Math.Round((double)gbPlcHelper.PLCRead("s2hMotorStatusT.CurrentPosition").data, 2);
                    var  y= Math.Round((double)gbPlcHelper.PLCRead("s2hMotorStatusY.CurrentPosition").data, 2);
                    Console.WriteLine($"{t}：{y}");
                    if (t > 120 && y < 0)
                    {
                        GlobalData.gbcranevm.IsVisible3 = true;
                    }
                    else if (t > 120 && y >= 0)
                    {
                        GlobalData.gbcranevm.IsVisible4 = true;
                    }
                    else if (t < 30 && y >= 0)
                    {
                        GlobalData.gbcranevm.IsVisible2 = true;
                    }
                    else if (t < 30 && y < 0) { GlobalData.gbcranevm.IsVisible = true; }

                    else
                    {
                        GlobalData.gbcranevm.IsVisible = false;
                        GlobalData.gbcranevm.IsVisible2 = false;
                        GlobalData.gbcranevm.IsVisible3 = false;
                        GlobalData.gbcranevm.IsVisible4 = false;
                    }
                    Thread.Sleep(100);
                }
            });


        }
        //public static void RefreshShelfDt()
        //{
        //    Task.Factory.StartNew(async () => {
        //        while (true)
        //        {
        //            try
        //            {
        //                List<TpLocation> dt = new List<TpLocation>();
        //                dt = await dbHelper.tpLocationdb.GetAllTpLocationAsync();

        //                foreach (var location in dt)
        //                {
        //                    // 检查Is_Occupied状态变化
        //                    if (!previousOccupiedStates.ContainsKey(location.Address))
        //                    {
        //                        previousOccupiedStates[location.Address] = location.Is_Occupied;

        //                        if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                        {
        //                            if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                            {

        //                                if (location.Is_Occupied == 1)
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 1;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 1;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 1;
        //                                    }
        //                                }
        //                                else if (location.Is_Prohibited == 1)
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 1;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 1;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 1;
        //                                    }
        //                                }
        //                                else
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
        //                                    }
        //                                }
        //                            }
        //                            else
        //                            {

        //                                if (location.Is_Prohibited == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 4;
        //                                }
        //                                else if (location.Is_Reserved == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 3;
        //                                }
        //                                else if (location.Is_Occupied == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 1;
        //                                }
        //                                else
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 0;
        //                                }
        //                            }
        //                        }
        //                    }
        //                    else if (previousOccupiedStates[location.Address] != location.Is_Occupied)
        //                    {
        //                        previousOccupiedStates[location.Address] = location.Is_Occupied;
        //                        if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                        {
        //                            if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                            {

        //                                if (location.Is_Occupied == 1)
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 1;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 1;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 1;
        //                                    }
        //                                }
        //                                else
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
        //                                    }
        //                                }
        //                            }
        //                            else
        //                            {
        //                                if (location.Is_Prohibited == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 4;
        //                                }
        //                                else if (location.Is_Reserved == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 3;
        //                                }
        //                                else if (location.Is_Occupied == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 1;
        //                                }
        //                                else
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 0;
        //                                }
        //                            }

        //                        }
        //                        // TODO: 处理Is_Occupied状态变化的业务逻辑
        //                    }

        //                    // 检查Is_Prohibited状态变化
        //                    if (!previousProhibitedStates.ContainsKey(location.Address))
        //                    {
        //                        previousProhibitedStates[location.Address] = location.Is_Prohibited;
        //                        if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                        {
        //                            if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                            {

        //                                if (location.Is_Prohibited == 1)
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
        //                                    }
        //                                }
        //                                else
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
        //                                    }
        //                                }
        //                            }
        //                            else
        //                            {
        //                                if (location.Is_Prohibited == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 4;
        //                                }
        //                                else if (location.Is_Reserved == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 3;
        //                                }
        //                                else if (location.Is_Occupied == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 1;
        //                                }
        //                                else
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 0;
        //                                }
        //                            }

        //                        }
        //                    }
        //                    else if (previousProhibitedStates[location.Address] != location.Is_Prohibited)
        //                    {
        //                        previousProhibitedStates[location.Address] = location.Is_Prohibited;
        //                        if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                        {
        //                            if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                            {

        //                                if (location.Is_Prohibited == 1)
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
        //                                    }
        //                                }
        //                                else
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
        //                                    }
        //                                }
        //                            }
        //                            else
        //                            {
        //                                if (location.Is_Prohibited == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 4;
        //                                }
        //                                else if (location.Is_Reserved == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 3;
        //                                }
        //                                else if (location.Is_Occupied == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 1;
        //                                }
        //                                else
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 0;
        //                                }
        //                            }
        //                        }
        //                        // TODO: 处理Is_Prohibited状态变化的业务逻辑
        //                    }

        //                    // 检查Is_Reserved状态变化
        //                    if (!previousReservedStates.ContainsKey(location.Address))
        //                    {
        //                        previousReservedStates[location.Address] = location.Is_Reserved;
        //                        if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                        {
        //                            if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                            {

        //                                if (location.Is_Reserved == 1)
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
        //                                    }
        //                                }
        //                                else
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
        //                                    }
        //                                }
        //                            }
        //                            else
        //                            {
        //                                if (location.Is_Prohibited == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 4;
        //                                }
        //                                else if (location.Is_Reserved == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 3;
        //                                }
        //                                else if (location.Is_Occupied == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 1;
        //                                }
        //                                else
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 0;
        //                                }
        //                            }

        //                        }
        //                    }
        //                    else if (previousReservedStates[location.Address] != location.Is_Reserved)
        //                    {
        //                        previousReservedStates[location.Address] = location.Is_Reserved;
        //                        if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                        {
        //                            if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
        //                            {

        //                                if (location.Is_Reserved == 1)
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
        //                                    }
        //                                }
        //                                else
        //                                {
        //                                    if (location.Address.Length == 7 && location.Address.EndsWith("01"))
        //                                    {

        //                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
        //                                    }
        //                                    else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
        //                                    }
        //                                    else
        //                                    {
        //                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
        //                                    }
        //                                }
        //                            }
        //                            else
        //                            {
        //                                if (location.Is_Prohibited == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 4;
        //                                }
        //                                else if (location.Is_Reserved == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 3;
        //                                }
        //                                else if (location.Is_Occupied == 1)
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 1;
        //                                }
        //                                else
        //                                {
        //                                    gbshelfvm[location.Address].ShelfColor = 0;
        //                                }
        //                            }

        //                        }
        //                        // TODO: 处理Is_Reserved状态变化的业务逻辑
        //                    }

        //                    // 检查Carrier_ID变化
        //                    if (!previousCarrierIds.ContainsKey(location.Address))
        //                    {
        //                        previousCarrierIds[location.Address] = location.Carrier_ID;
        //                    }
        //                    else if (previousCarrierIds[location.Address] != location.Carrier_ID)
        //                    {
        //                        previousCarrierIds[location.Address] = location.Carrier_ID;
        //                        // TODO: 处理Carrier_ID变化的业务逻辑
        //                    }
        //                }

        //            }
        //            catch (Exception ex)
        //            {

        //                throw;
        //            }

        //            await Task.Delay(100);
        //        }
        //    });

        //}
        static int con = 0;
        //Port 、Shelf刷新以及统计
        public static void RefreshShelfDt()
        {
            Task.Factory.StartNew(async () =>
            {
                while (true)
                {
                    try
                    {
                        List<TpLocation> dt = new List<TpLocation>();
                        dt = await dbHelper.tpLocationdb.GetAllTpLocationAsync();
                        GlobalData.gbtopScInfovm.TotlaShelf = dt.Where(t => t.Type == "1").ToList().Count;

                        //计算空的Shelf 
                        gbtopScInfovm.EmptyShelf = dt.Where(t => t.Is_Occupied == 0 && t.Type == "1" && t.Is_Reserved == 0 && t.Is_Prohibited == 0).ToList().Count;
                        foreach (var location in dt)
                        {
                            con++;
                            // 刚加载时候先存起来数据
                            if (!previousOccupiedStates.ContainsKey(location.Address))
                            {
                                TpResult result = new TpResult();
                                result.Is_Occupied=location.Is_Occupied;
                                result.Is_Prohibited=location.Is_Prohibited;
                                result.Is_Reserved=location.Is_Reserved;
                                previousOccupiedStates.Add(location.Address, result);
                                if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))//先判断全局变量vm中是否缓存了该shelf或者port
                                {
                                    if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))//先判断Port情况，portvm中的key都是5位数
                                    {
                                        if (location.Address.Length==7 && previousOccupiedStates[location.Address.Substring(0, 5)].Is_Prohibited==1)//判断Port是否被禁止，因为禁止是整个port禁止
                                        {
                                            gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 4;
                                            gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 4;
                                            gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 4;
                                        }
                                        else if (location.Is_Prohibited == 1)
                                        {
                                            gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 4;
                                            gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 4;
                                            gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 4;
                                        }
                                        else
                                        {
                                            if (location.Is_Occupied == 1)
                                            {
                                                if (location.Address.Length == 7 && location.Address.EndsWith("01"))//颜色就要区分是LP、op、bd
                                                {
                                                    if (location.Carrier_ID.Contains("ER_")|| location.Carrier_ID.Contains("ED_")||location.Carrier_ID.Contains("ED_"))
                                                    {
                                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
                                                    }
                                                    else
                                                    {
                                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 1;
                                                    }
                                                  
                                                }
                                                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                                                {
                                                    if (location.Carrier_ID.Contains("ER_") || location.Carrier_ID.Contains("ED_") || location.Carrier_ID.Contains("ED_"))
                                                    {
                                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
                                                    }
                                                    else
                                                    {
                                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 1;
                                                    }
                                                       
                                                }
                                                else
                                                {
                                                    if (location.Carrier_ID.Contains("ER_") || location.Carrier_ID.Contains("ED_") || location.Carrier_ID.Contains("ED_"))
                                                    {
                                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
                                                    }
                                                    else
                                                    {
                                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 1;
                                                    }
                                                  
                                                }
                                            }
                                            else if (location.Is_Reserved == 1)
                                            {
                                                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                                                {

                                                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 3;
                                                }
                                                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                                                {
                                                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 3;
                                                }
                                                else
                                                {
                                                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 3;
                                                }
                                            }
                                            else
                                            {
                                                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                                                {

                                                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
                                                }
                                                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                                                {
                                                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
                                                }
                                                else
                                                {
                                                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
                                                }
                                            }
                                        }
                                    }
                                    else
                                    {
                                        gbshelfvm[location.Address].shelfName = location.Name;
                                        if (location.Is_Prohibited == 1)
                                        {
                                            gbshelfvm[location.Address].ShelfColor = 4;
                                        }
                                        else if (location.Is_Reserved == 1)
                                        {
                                            gbshelfvm[location.Address].ShelfColor = 3;
                                        }
                                        else if (location.Is_Occupied == 1)
                                        {
                                            if (location.Carrier_ID.Contains("ER_") || location.Carrier_ID.Contains("ED_") || location.Carrier_ID.Contains("ED_"))
                                            {
                                                gbshelfvm[location.Address].ShelfColor = 2;
                                            }
                                            else
                                            {
                                                gbshelfvm[location.Address].ShelfColor = 1;
                                            }
                                        }
                                        else
                                        {
                                            gbshelfvm[location.Address].ShelfColor = 0;
                                        }
                                    }
                                }
                            }
                            else if (previousOccupiedStates[location.Address].Is_Occupied != location.Is_Occupied || previousOccupiedStates[location.Address].Is_Prohibited != location.Is_Prohibited || previousOccupiedStates[location.Address].Is_Reserved != location.Is_Reserved)
                            {
                                previousOccupiedStates[location.Address].Is_Occupied = location.Is_Occupied;
                                previousOccupiedStates[location.Address].Is_Prohibited = location.Is_Prohibited;
                                previousOccupiedStates[location.Address].Is_Reserved = location.Is_Reserved;
                                if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                                {
                                    if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                                    {

                                        if (location.Address.Length == 7 && previousOccupiedStates[location.Address.Substring(0, 5)].Is_Prohibited == 1)
                                        {
                                            gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 4;
                                            gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 4;
                                            gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 4;
                                        }
                                        else if (location.Is_Prohibited == 1)
                                        {
                                            gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 4;
                                            gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 4;
                                            gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 4;
                                        }
                                        else
                                        {
                                            if (location.Is_Occupied == 1)
                                            {
                                                if (location.Address.Length == 7 && location.Address.EndsWith("01"))//颜色就要区分是LP、op、bd
                                                {
                                                    if (location.Carrier_ID.Contains("ER_") || location.Carrier_ID.Contains("ED_") || location.Carrier_ID.Contains("ED_"))
                                                    {
                                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
                                                    }
                                                    else
                                                    {
                                                        gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 1;
                                                    }

                                                }
                                                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                                                {
                                                    if (location.Carrier_ID.Contains("ER_") || location.Carrier_ID.Contains("ED_") || location.Carrier_ID.Contains("ED_"))
                                                    {
                                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
                                                    }
                                                    else
                                                    {
                                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 1;
                                                    }

                                                }
                                                else
                                                {
                                                    if (location.Carrier_ID.Contains("ER_") || location.Carrier_ID.Contains("ED_") || location.Carrier_ID.Contains("ED_"))
                                                    {
                                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
                                                    }
                                                    else
                                                    {
                                                        gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 1;
                                                    }

                                                }
                                            }
                                            else if (location.Is_Reserved == 1)
                                            {
                                                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                                                {

                                                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 3;
                                                }
                                                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                                                {
                                                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 3;
                                                }
                                                else
                                                {
                                                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 3;
                                                }
                                            }
                                            else
                                            {
                                                gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
                                                gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
                                                gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
                                                //if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                                                //{

                                                //    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
                                                //}
                                                //else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                                                //{
                                                //    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
                                                //}
                                                //else
                                                //{
                                                //    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
                                                //}
                                            }
                                        }
                                    }
                                    else
                                    {
                                        if (location.Is_Prohibited == 1)
                                        {
                                            gbshelfvm[location.Address].ShelfColor = 4;
                                        }
                                        else if (location.Is_Reserved == 1)
                                        {
                                            gbshelfvm[location.Address].ShelfColor = 3;
                                        }
                                        else if (location.Is_Occupied == 1)
                                        {
                                            if (location.Carrier_ID.Contains("ER_") || location.Carrier_ID.Contains("ED_") || location.Carrier_ID.Contains("ED_"))
                                            {
                                                gbshelfvm[location.Address].ShelfColor = 2;
                                            }
                                            else
                                            {
                                                gbshelfvm[location.Address].ShelfColor = 1;
                                            }
                                        }
                                        else
                                        {
                                            gbshelfvm[location.Address].ShelfColor = 0;
                                        }
                                    }

                                }
                                // TODO: 处理Is_Occupied状态变化的业务逻辑
                            }
                            #region
                            // 检查Is_Prohibited状态变化
                            //if (!previousProhibitedStates.ContainsKey(location.Address))
                            //{
                            //    previousProhibitedStates[location.Address] = location.Is_Prohibited;
                            //    if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                            //    {
                            //        if ( gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                            //        {

                            //            if (location.Is_Prohibited == 1)
                            //            {
                            //                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                            //                {

                            //                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
                            //                }
                            //                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
                            //                }
                            //                else
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
                            //                }
                            //            }
                            //            else
                            //            {
                            //                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                            //                {

                            //                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
                            //                }
                            //                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
                            //                }
                            //                else
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
                            //                }
                            //            }
                            //        }
                            //        else
                            //        {
                            //            if (location.Is_Prohibited == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 4;
                            //            }
                            //            else if (location.Is_Reserved == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 3;
                            //            }
                            //            else if (location.Is_Occupied == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 1;
                            //            }
                            //            else
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 0;
                            //            }
                            //        }

                            //    }
                            //}
                            //else if (previousProhibitedStates[location.Address] != location.Is_Prohibited)
                            //{
                            //    previousProhibitedStates[location.Address] = location.Is_Prohibited;
                            //    if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                            //    {
                            //        if ( gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                            //        {

                            //            if (location.Is_Prohibited == 1)
                            //            {
                            //                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                            //                {

                            //                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
                            //                }
                            //                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
                            //                }
                            //                else
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
                            //                }
                            //            }
                            //            else
                            //            {
                            //                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                            //                {

                            //                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
                            //                }
                            //                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
                            //                }
                            //                else
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
                            //                }
                            //            }
                            //        }
                            //        else
                            //        {
                            //            if (location.Is_Prohibited == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 4;
                            //            }
                            //            else if (location.Is_Reserved == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 3;
                            //            }
                            //            else if (location.Is_Occupied == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 1;
                            //            }
                            //            else
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 0;
                            //            }
                            //        }
                            //    }
                            //    // TODO: 处理Is_Prohibited状态变化的业务逻辑
                            //}

                            // 检查Is_Reserved状态变化
                            //if (!previousReservedStates.ContainsKey(location.Address))
                            //{
                            //    previousReservedStates[location.Address] = location.Is_Reserved;
                            //    if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                            //    {
                            //        if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                            //        {

                            //            if (location.Is_Reserved == 1)
                            //            {
                            //                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                            //                {

                            //                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
                            //                }
                            //                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
                            //                }
                            //                else
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
                            //                }
                            //            }
                            //            else
                            //            {
                            //                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                            //                {

                            //                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
                            //                }
                            //                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
                            //                }
                            //                else
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
                            //                }
                            //            }
                            //        }
                            //        else
                            //        {
                            //            if (location.Is_Prohibited == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 4;
                            //            }
                            //            else if (location.Is_Reserved == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 3;
                            //            }
                            //            else if (location.Is_Occupied == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 1;
                            //            }
                            //            else
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 0;
                            //            }
                            //        }

                            //    }
                            //}
                            //else if (previousReservedStates[location.Address] != location.Is_Reserved)
                            //{
                            //    previousReservedStates[location.Address] = location.Is_Reserved;
                            //    if (gbshelfvm.ContainsKey(location.Address) || gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                            //    {
                            //        if (gbPortViewModels.ContainsKey(location.Address.Substring(0, 5)))
                            //        {

                            //            if (location.Is_Reserved == 1)
                            //            {
                            //                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                            //                {

                            //                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 2;
                            //                }
                            //                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 2;
                            //                }
                            //                else
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 2;
                            //                }
                            //            }
                            //            else
                            //            {
                            //                if (location.Address.Length == 7 && location.Address.EndsWith("01"))
                            //                {

                            //                    gbPortViewModels[location.Address.Substring(0, 5)].Bd.PortColor = 0;
                            //                }
                            //                else if (location.Address.Length == 7 && location.Address.EndsWith("02"))
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Op.PortColor = 0;
                            //                }
                            //                else
                            //                {
                            //                    gbPortViewModels[location.Address.Substring(startIndex: 0, 5)].Lp.PortColor = 0;
                            //                }
                            //            }
                            //        }
                            //        else
                            //        {
                            //            if (location.Is_Prohibited == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 4;
                            //            }
                            //            else if (location.Is_Reserved == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 3;
                            //            }
                            //            else if (location.Is_Occupied == 1)
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 1;
                            //            }
                            //            else
                            //            {
                            //                gbshelfvm[location.Address].ShelfColor = 0;
                            //            }
                            //        }

                            //    }
                            //    // TODO: 处理Is_Reserved状态变化的业务逻辑
                            //}
                            #endregion
                            // 检查Carrier_ID变化
                            //if (!previousCarrierIds.ContainsKey(location.Address))
                            //{
                            //    previousCarrierIds[location.Address] = location.Carrier_ID;
                            //}
                            //else if (previousCarrierIds[location.Address] != location.Carrier_ID)
                            //{
                            //    previousCarrierIds[location.Address] = location.Carrier_ID;
                            //    // TODO: 处理Carrier_ID变化的业务逻辑
                            //}
                        }

                    }
                    catch (Exception ex)
                    {
                        Proj.Log.Logger.Instance.ExceptionLog("RefreshShelfDt" + ex.Message + ", Stack: " + ex.StackTrace);
                    }

                    await Task.Delay(100);
                }
            });

        }
         //搬送任务界面显示刷新
        public static void RefreshTransferInfo()
        {
            _refreshTransferCts.Cancel(); // 取消现有的任务
            _refreshTransferCts = new CancellationTokenSource();
            var token = _refreshTransferCts.Token;

            Task.Run(async () => {
                while (!token.IsCancellationRequested)
                {
                    try
                    {
                        // 在后台线程执行数据库查询
                        var transferList = await GlobalData.dbHelper.transferdb.GetAllTpTransferAsync();
                        var existingTransfers = GlobalData.gbtransferViewModels.ToList();

                        // 使用Dispatcher更新UI
                        for (int i = existingTransfers.Count - 1; i >= 0; i--)
                        {
                            var existing = existingTransfers[i];
                            if (!transferList.Any(t => t.Id == existing.Id))
                            {
                                transfercount?.Invoke(false, existing);
                                //GlobalData.gbtransferViewModels.Remove(existing);
                            }
                        }

                        // 更新或添加新记录
                        foreach (var transfer in transferList)
                        {
                            TransferState state = (TransferState)Convert.ToUInt32(transfer.State);
                            DateTime createTime = Convert.ToDateTime(transfer.Create_Time);

                            string strCurrLocation = "";
                            string strCarrierID = transfer.Carrier_ID;
                            if (strCarrierID != "")
                            {
                                TpCarrier carrier = GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByIdAsync(strCarrierID).Result;
                                if (carrier != null)
                                {
                                    strCurrLocation = carrier.Location;
                                }
                            }
                            var existingTransfer = GlobalData.gbtransferViewModels.FirstOrDefault(t => t.Id == transfer.Id);
                            TransferViewModel current = existingTransfer ?? new TransferViewModel();

                            bool hasChanges = false;
                            if (existingTransfer == null)
                            {
                                hasChanges = true;
                                current.Id = transfer.Id;
                            }

                            // 检查各字段是否有变化
                            if (current.CarrierId != transfer.Carrier_ID)
                            {
                                current.CarrierId = transfer.Carrier_ID;
                                hasChanges = true;
                            }
                            if (current.SourceLocation != transfer.Source_Location)
                            {
                                current.SourceLocation = transfer.Source_Location;
                                hasChanges = true;
                            }
                            if (current.DestLocation != transfer.Dest_Location)
                            {
                                current.DestLocation = transfer.Dest_Location;
                                hasChanges = true;
                            }
                            if (current.Priority != transfer.Priority)
                            {
                                current.Priority = transfer.Priority;
                                hasChanges = true;
                            }
                            if (current.CommandType != transfer.Command_Type)
                            {
                                current.CommandType = transfer.Command_Type;
                                hasChanges = true;
                            }
                            if (current.CreateTime != createTime)
                            {
                                current.CreateTime = createTime;
                                hasChanges = true;
                            }
                            if (current.State != state.ToString())
                            {
                                current.State = state.ToString();
                                hasChanges = true;
                            }
                            if (current.CmdSource != transfer.Cmd_Source)
                            {
                                current.CmdSource = transfer.Cmd_Source;
                                hasChanges = true;
                            }
                            if (current.DelayReason != transfer.Delay_Reason)
                            {
                                current.DelayReason = transfer.Delay_Reason;
                                hasChanges = true;
                            }
                            if (current.CurLocation != strCurrLocation)
                            {
                                current.CurLocation = strCurrLocation;
                                hasChanges = true;
                            }

                            current.WaitingTime = (DateTime.Now - createTime).TotalMinutes.ToString("0.0");

                            if (hasChanges && existingTransfer == null)
                            {
                                transfercount?.Invoke(true, current);
                                //GlobalData.gbtransferViewModels.Add(current);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Proj.Log.Logger.Instance.ExceptionLog("RefreshTransferInfo" + ex.Message + ", Stack: " + ex.StackTrace);
                        // Debug.WriteLine($"RefreshTransferInfo error: {ex.Message}");
                    }

                    await Task.Delay(1000, token); // 增加延迟时间到1秒，并支持取消
                }
            }, token);
        }

      
    }
}
