﻿using EmulatorClient;
using N2Purge.ViewModel;
using N2Purge.ViewModel;
using OxyPlot;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace N2Purge.silan
{
    /// <summary>
    /// frmAction.xaml 的交互逻辑
    /// </summary>
    public partial class frmAction : UserControl
    {
    
        private bool _isLoad = false;
        public frmAction()
        {
            InitializeComponent();
            
           
        }

        private void FrmAction_Loaded(object sender, RoutedEventArgs e)
        {
            if (!_isLoad)
            {
                if (!System.ComponentModel.DesignerProperties.GetIsInDesignMode(this))
                {
                    transfer.DataContext = GlobalData.frmActionViewModel.transferExcuteViewModel;
                    install.DataContext = GlobalData.frmActionViewModel.installViewModel;
                    GlobalData.frmActionViewModel.installViewModel.InstallCstState = "Completed";
                    List<string> lst = new List<string>() { "Success", "Mismatch", "Failed" };
                    InstallIdResult.ItemsSource=lst;
                    move.DataContext = GlobalData.frmActionViewModel.moveViewModel;
                    remove.DataContext = GlobalData.frmActionViewModel.removeViewModel;
                    scan.DataContext = GlobalData.frmActionViewModel.scanViewModel;
                    GlobalData.frmActionViewModel.scanViewModel.Priority = 999;
                    GlobalData.ShelfRightClick += Shelfclick;
                    GlobalData.ControlLeftClick += ShelfLeftclick;
                    GlobalData.PortRightClick += GlobalData_PortRightClick; ;
                }
            }
           
        }

        private async void GlobalData_PortRightClick(string arg1, PortViewModel model)
        {
            switch (arg1)
            {
                case "Transfer":
                    tb.SelectedIndex = 0;
                    if (model != null)
                    {
                        GlobalData.frmActionViewModel.transferExcuteViewModel.SourceLocation = model.PortLocation.Substring(0,5);
                        var d = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(model.PortLocation);
                        if (d != null && d.Count > 0)
                        {
                            GlobalData.frmActionViewModel.transferExcuteViewModel.CarrierId = d[0].Id;
                        }
                    }
                    break;
                case "Install":
                    tb.SelectedIndex = 1;
                    if (model != null)
                    {
                        var d = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(model.PortLocation);
                        if (d == null || d.Count > 0)
                        {
                            MessageBox.Show("This Shlef has Carrier");
                            return;
                        }
                        else
                        {
                            GlobalData.frmActionViewModel.installViewModel.SourceLocation = model.PortLocation;
                        }
                    }
                    break;

                case "Remove":
                    tb.SelectedIndex = 2;
                    if (model != null)
                    {
                        GlobalData.frmActionViewModel.removeViewModel.Location = model.PortLocation;
                        var d = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(model.PortLocation);
                        if (d != null && d.Count > 0)
                        {
                            GlobalData.frmActionViewModel.removeViewModel.CstID = d[0].Id;
                        }
                    }
                    break;
                case "Scan":
                    tb.SelectedIndex = 3;
                    if (model != null)
                    {
                        GlobalData.frmActionViewModel.removeViewModel.Location = model.PortLocation;
                        var d = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(model.PortLocation);
                        if (d != null && d.Count > 0)
                        {
                            GlobalData.frmActionViewModel.removeViewModel.CstID = d[0].Id;
                        }
                    }
                    break;
                case "Move":
                    tb.SelectedIndex = 4;
                    break;
            }
        }

        private void ShelfLeftclick(string DestLocation)
        {
            if (!string.IsNullOrEmpty(DestLocation))
            {
                GlobalData.frmActionViewModel.transferExcuteViewModel.DestLocation = DestLocation.Substring(0,5);
            }
        }

        private async void Shelfclick(string arg1, ShelfViewModel model)
        {
            switch (arg1)
            {
                case "Transfer":
                    tb.SelectedIndex = 0;
                    if (model!=null)
                    {
                        GlobalData.frmActionViewModel.transferExcuteViewModel.SourceLocation = model.ShelfLocation;
                        var d =await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(model.ShelfLocation);
                        if (d!=null&& d.Count > 0)
                        {
                            GlobalData.frmActionViewModel.transferExcuteViewModel.CarrierId = d[0].Id;
                        }
                    }
                    break;
                case "Install":
                    tb.SelectedIndex = 1;
                    if (model != null)
                    {
                        var d = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(model.ShelfLocation);
                        if (d == null||d.Count>0)
                        {
                            MessageBox.Show("This Shlef has Carrier");
                            return;
                        }
                        else
                        {
                            GlobalData.frmActionViewModel.installViewModel.SourceLocation = model.ShelfLocation;
                        }
                    }
                    break;
                   
                case "Remove":
                    tb.SelectedIndex = 2;
                    if (model != null)
                    {
                        GlobalData.frmActionViewModel.removeViewModel.Location = model.ShelfLocation;
                        var d = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(model.ShelfLocation);
                        if (d != null && d.Count > 0)
                        {
                            GlobalData.frmActionViewModel.removeViewModel.CstID = d[0].Id;
                        }
                    }
                    break;
                case "Scan":
                    tb.SelectedIndex = 3;
                    if (model != null)
                    {
                        GlobalData.frmActionViewModel.removeViewModel.Location = model.ShelfLocation;
                        var d = await GlobalData.dbHelper.tpCarrierdb.GetTpCarrierByLocationAsync(model.ShelfLocation);
                        if (d != null && d.Count > 0)
                        {
                            GlobalData.frmActionViewModel.removeViewModel.CstID = d[0].Id;
                        }
                    }
                    break;
                case "Move":
                    tb.SelectedIndex = 4;
                    break;
            }
        }
    }
}
