﻿using DBEntity;
using N2Purge.ViewModel;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace N2Purge.ViewModel
{
    public class TransferExcuteViewModel : VmPropertyChange
    {
        private string _id;
        private string _carrierId;
        private int _sourceLocation;
        private int _destLocation;
        private int _priority;
        private string _commandType;
        private DateTime _createTime;
        private string _state;
        private string _cmdSource;
        private string _delayReason;
        private string _curLocation;
        private TimeSpan _waitingTime;
        private Dictionary<int, string> _SourceList = new Dictionary<int, string>();
        private Dictionary<int, string> _DestList = new Dictionary<int, string>();

        public TransferExcuteViewModel()
        {
            Task.Factory.StartNew(() => {
                DatabaseHelper<tp_path_point> databaseHelper = new DatabaseHelper<tp_path_point>(ConfigurationManager.ConnectionStrings["StkCConnection"].ConnectionString);
                var pointList = databaseHelper.QueryAsync($"is_exit=1 or is_enter=1").Result;
                if (pointList != null && pointList.Count > 0)
                {
                    foreach (var item in pointList)
                    {
                        if (item.is_enter)
                        {
                            _SourceList[item.point_id] = item.disp_row + "," + item.disp_column;
                        }
                        if (item.is_exit)
                        {
                            _DestList[item.point_id] = item.disp_row + "," + item.disp_column;
                        }
                    }
                }
            });
        }
        public string Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged(nameof(Id));
            }
        }

        public string CarrierId
        {
            get => _carrierId;
            set
            {
                _carrierId = value;
                OnPropertyChanged(nameof(CarrierId));
            }
        }

        public int SourceLocation
        {
            get => _sourceLocation;
            set
            {
                _sourceLocation = value;
                OnPropertyChanged(nameof(SourceLocation));
            }
        }

        public int DestLocation
        {
            get => _destLocation;
            set
            {
                _destLocation = value;
                OnPropertyChanged(nameof(DestLocation));
            }
        }
        public Dictionary<int, string> SourceList
        {
            get => _SourceList;
            set
            {
                _SourceList = value;
                OnPropertyChanged(nameof(SourceList));
            }
        }

        public Dictionary<int, string> DestList
        {
            get => _DestList;
            set
            {
                _DestList = value;
                OnPropertyChanged(nameof(DestList));
            }
        }
        public int Priority
        {
            get => _priority;
            set
            {
                _priority = value;
                OnPropertyChanged(nameof(Priority));
            }
        }

        public string CommandType
        {
            get => _commandType;
            set
            {
                _commandType = value;
                OnPropertyChanged(nameof(CommandType));
            }
        }

        public DateTime CreateTime
        {
            get => _createTime;
            set
            {
                _createTime = value;
                OnPropertyChanged(nameof(CreateTime));
            }
        }

        public string State
        {
            get => _state;
            set
            {
                _state = value;
                OnPropertyChanged(nameof(State));
            }
        }

        public string CmdSource
        {
            get => _cmdSource;
            set
            {
                _cmdSource = value;
                OnPropertyChanged(nameof(CmdSource));
            }
        }

        public string DelayReason
        {
            get => _delayReason;
            set
            {
                _delayReason = value;
                OnPropertyChanged(nameof(DelayReason));
            }
        }

        public string CurLocation
        {
            get => _curLocation;
            set
            {
                _curLocation = value;
                OnPropertyChanged(nameof(CurLocation));
            }
        }

        public TimeSpan WaitingTime
        {
            get => _waitingTime;
            set
            {
                _waitingTime = value;
                OnPropertyChanged(nameof(WaitingTime));
            }
        }

    }
}