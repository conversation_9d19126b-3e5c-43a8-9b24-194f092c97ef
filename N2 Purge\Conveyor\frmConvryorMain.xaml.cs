using DBEntity;
using EmulatorClient;
using log4net.Util;
using Microsoft.IdentityModel.Tokens;
using N2Purge.silan;
using N2Purge.ViewModel;
using Proj.Log;
using System.Collections.Concurrent;
using System.Configuration;
using System.Text;
using System.Timers;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using static N2Purge.Conveyor.frmConvryorMain;
using tp_point_config = DBEntity.tp_point_config;

namespace N2Purge.Conveyor
{
    /// <summary>
    /// frmConvryorMain.xaml 的交互逻辑
    /// </summary>
    public partial class frmConvryorMain : UserControl
    {
        /// <summary>
        /// 数据库连接字符串
        /// </summary>
        public static string constr = "";
        private const string _conveyorName = "CARRIER_EMULATOR";

        /// <summary>
        /// 静态实例，用于其他类访问
        /// </summary>
        public static frmConvryorMain? Instance { get; private set; }

        /// <summary>
        /// 获取警告信息
        /// </summary>
        private const string Get_Warnning_Info = "CARRIER_EMULATOR.Alert";

        /// <summary>
        /// 获取点位信息
        /// </summary>
        private const string Get_State_Info = "CARRIER_EMULATOR.{0}.foupid";

        #region Nested Class
        /// <summary>
        /// 页面元素和控件描述结构
        /// </summary>
        public class ObjectContent
        {
            public DBEntity.tp_path_point PointInfo { get; set; }
            public Type ControlType { get; set; }
            public Shelf Shelf { get; set; }
            public ShelfViewModel ShelfViewModel { get; set; } // 添加ViewModel属性
        }

        /// <summary>
        /// 点位背景色
        /// </summary>
        public enum ShelfColor : uint
        {
            ContainGoods=2, // 有货物
            OnTrans=1, // 搬运中
            Empty=0, // 空闲
            Warning= 9 // 警告状态
        }
        /// <summary>
        /// 描述运动状态
        /// </summary>
        public enum ActionState : uint
        {
            /// <summary>
            /// 未初始化状态
            /// </summary>
            None = 0,
            /// <summary>
            /// 进入点位
            /// </summary>
            Entry = 1,
            /// <summary>
            /// 点位搬运中
            /// </summary>
            OnTrans = 2,
            /// <summary>
            /// 离开点位,搬运完成
            /// </summary>
            Exit = 4
        }

        /// <summary>
        /// 监控点位,读取plc数据
        /// </summary>
        public interface IConveyor
        {
            DateTime? EntryOn { get; }// 进入时间
            string PointID { get; set; }// 点位ID
            string Address { get; set; }// Address 地址
            string FoupID { get; }// 货物ID,如果没有应该设置为空
            DateTime? ExitOn { get; }// 离开时间,如果没有应该设置为NULL
            bool IsRun { get; }
            ActionState PrevState { get; set; }// 上一个状态
            ActionState CurrentState { get; set; }// 当前状态
            bool IsEntry { get; set; }  // 是否是入口点位,如果是则需要发送Semi事件
            bool IsExit { get; set; }  // 是否是出口点位,如果是则需要发送Semi事件
            /// <summary>
            /// 主处理循环
            /// </summary>
            void MainLoop();

            /// <summary>
            /// 开始处理
            /// </summary>
            void Start();

            /// <summary>
            /// 停止处理
            /// </summary>
            void Stop();

            /// <summary>
            /// 检查是否有货物,通过读取plc
            /// </summary>
            /// <returns></returns>
            bool CheckHasFoup(out string foupid);
        }

        /// <summary>
        /// 描述具体的一个点位
        /// </summary>
        public class Conveyor : IConveyor
        {
            private Logger _Log = Proj.Log.Logger.Instance;
            // 添加对主窗体的引用
            private frmConvryorMain _mainForm;

            public Conveyor(frmConvryorMain mainForm)
            {
                _mainForm = mainForm;
            }

            #region 静态方法
            public static List<TransmissionPath‌> TransmissionPaths = new List<TransmissionPath‌>();

            public static void RemoveByFoupID(string foupid)
            {
                // 移除指定FoupID的传送路径
                TransmissionPaths.RemoveAll(p => p.FoupID == foupid);
            }
            public static void RemoveCompletedPaths()
            {
                // 移除已经完成的传送路径
                TransmissionPaths.RemoveAll(p => p.ExitOn.HasValue);
            }
            public static void AddPath(TransmissionPath‌ path)
            {
                // 添加新的传送路径
                TransmissionPaths.Add(path);
            }

            public static TransmissionPath‌ GetPathByFoupID(string foupid)
            {
                // 获取指定FoupID的传送路径
                return TransmissionPaths.FirstOrDefault(p => p.FoupID == foupid);
            }

            public static void AddStation(TransmissionPath‌ path, IConveyor station)
            {
                // 添加站点到传送路径，避免重复添加
                if (path != null && station != null)
                {
                    // 检查是否已经存在相同的站点（基于PointID和时间）
                    var existingStation = path.Stations.FirstOrDefault(s =>
                        s.PointID == station.PointID &&
                        s.EntryOn == station.EntryOn);

                    if (existingStation == null)
                    {
                        path.Stations.Add(station);
                        Proj.Log.Logger.Instance.OperationLog($"添加站点到路径：PointID={station.PointID}, 路径ID={path.Guid}");
                    }
                    else
                    {
                        Proj.Log.Logger.Instance.OperationLog($"站点已存在，跳过添加：PointID={station.PointID}, 路径ID={path.Guid}");
                    }
                }
            }
            #endregion

            private string _FoupID = "";
            private DateTime? _ExitOn = null;
            private DateTime? _EntryOn = null;
            private bool _IsRun = true;

            public DateTime? EntryOn => _ExitOn;
            public string Address { get; set; } = "";// Address 地址
            public string PointID { get; set; } = "";
            public string FoupID => _FoupID;
            public DateTime? ExitOn => _ExitOn;

            public bool IsRun => _IsRun;// 是否正在运行

            public ActionState PrevState { get; set; }// 上一个状态

            public ActionState CurrentState { get; set; }// 当前状态

            public bool IsEntry { get; set; } = false;// 当前状态
            public bool IsExit { get; set; } = false;// 当前状态

            /// <summary>
            /// 更新传送路径的Carrier信息
            /// </summary>
            private void UpdateTpCarrier()
            {
                if (string.IsNullOrEmpty(FoupID)) return;
                DatabaseHelper<TpCarrier> tpCarrierSet = new DatabaseHelper<TpCarrier>(constr);
                TpCarrier tpCarrier = new TpCarrier
                {
                    Id = FoupID,
                    Location = $"{Address}",
                    Id_Read_Status = "0", // 假设状态为OK
                    Install_Time = DateTime.Now,
                    State = ((int)CurrentState).ToString(),
                    Comment = $"PointID: {PointID}, FoupID: {_FoupID}, EntryOn: {_EntryOn}, ExitOn: {_ExitOn}"
                };
                tpCarrierSet.QueryFirstAsync($"Id = {PointID}").ContinueWith(task =>
                {
                    if (task.IsCompleted && task.Result != null)
                    {
                        task.Result.Location = $"{Address}";
                        // 更新现有记录
                        tpCarrierSet.UpdateAsync(task.Result, $"Id = {PointID}").Wait();
                    }
                    else
                    {
                        // 插入新记录
                        tpCarrierSet.AddAsync(tpCarrier).Wait();
                    }
                });
            }

            /// <summary>
            /// 更新location信息
            /// </summary>
            private void UpdateLocation()
            {
                DatabaseHelper<TpLocation> tpLocationSet = new DatabaseHelper<TpLocation>(constr);
                TpLocation tpLocation = new TpLocation
                {
                    Address = Address,
                    Name = "",
                    Type = "1",
                    Disp_Column = int.Parse(this.Address.Split(',')[1]),
                    Disp_Row = int.Parse(this.Address.Split(',')[0]),
                    Is_Occupied = CurrentState == ActionState.OnTrans ? 1 : 0,
                    Is_Reserved = 0, // 假设没有保留状态
                    Is_Prohibited = 0, // 假设没有禁止状态
                    Zone_Name = "",
                    Carrier_ID = FoupID,
                    Comment = $"PointID: {PointID}, FoupID: {_FoupID}, EntryOn: {_EntryOn}, ExitOn: {_ExitOn}",
                    Order = 9999
                };

                tpLocationSet.QueryFirstAsync($"Address = {Address}").ContinueWith(task =>
                {
                    if (task.IsCompleted && task.Result != null)
                    {
                        task.Result.Carrier_ID = $"{FoupID}";
                        task.Result.Comment = $"{tpLocation.Comment}";
                        // 更新现有记录
                        tpLocationSet.UpdateAsync(task.Result, $"Address = {Address}").Wait();
                    }
                    else
                    {
                        // 插入新记录
                        tpLocationSet.AddAsync(tpLocation).Wait();
                    }
                });
            }

            /// <summary>
            /// 主处理循环
            /// </summary>
            public void MainLoop()
            {
                _ = Task.Factory.StartNew(() =>
                {
                    int interval = int.Parse(ConfigurationManager.AppSettings["PointLoopInterval"]);
                    int count = 0;
                    int maxTime = int.Parse(ConfigurationManager.AppSettings["PointBlockTime"]);
                    string currentFoupId = string.Empty;
                    bool stationAdded = false; // 标记当前货物是否已添加到路径

                    _Log.OperationLog($"[{PointID}] MainLoop 开始运行，点位类型：入口={IsEntry}, 出口={IsExit}");

                    while (_IsRun)
                    {
                        try
                        {
                            bool hasFoup = CheckHasFoup(out var foupid);

                            if (!hasFoup)// 无货
                            {
                                UpdateShelfByPointID(PointID, (int)ShelfColor.Empty, foupid);
                                System.Threading.Thread.Sleep(interval);
                                continue;
                            }
                            else// 有货
                            {
                                UpdateShelfByPointID(PointID, (int)ShelfColor.ContainGoods, foupid);
                                System.Threading.Thread.Sleep(interval/2);
                                continue;
                            }
                                
                            #region 处理货物状态
                            if ((PrevState == ActionState.None || PrevState == ActionState.Exit) && hasFoup)
                            {
                                // 无货->有货：货物进入
                                _EntryOn = DateTime.Now;
                                currentFoupId = foupid;
                                CurrentState = ActionState.Entry;
                                PrevState = ActionState.Entry;
                                stationAdded = false; // 重置标记

                                _Log.OperationLog($"[{PointID}] 货物进入：FoupID={foupid}, 时间={_EntryOn:yyyy-MM-dd HH:mm:ss.fff}");
                                //UpdateTpCarrier();
                                // 更新Shelf控件颜色为绿色（有货物）并显示货物ID
                                UpdateShelfByPointID(PointID, (int)ShelfColor.ContainGoods, foupid);

                                if (IsEntry)
                                {
                                    // 入口点位：创建新路径并添加自己
                                    TransmissionPath‌ path = new TransmissionPath‌(foupid, PointID, _EntryOn);
                                    AddPath(path);
                                    _Log.OperationLog($"[{PointID}] 创建新传送路径：FoupID={foupid}, EntryPointID={PointID}, 路径ID={path.Guid}");
                                }
                                else
                                {
                                    // 非入口点位：查找现有路径但不立即添加Station
                                    var existingPath = GetPathByFoupID(foupid);
                                    if (existingPath != null)
                                    {
                                        _Log.OperationLog($"[{PointID}] 找到现有路径：FoupID={foupid}, 路径ID={existingPath.Guid}");
                                    }
                                    else
                                    {
                                        _Log.OperationLog($"[{PointID}] 警告：非入口点位检测到货物，但未找到对应传送路径：FoupID={foupid}");
                                    }
                                }
                            }
                            else if (PrevState == ActionState.Entry && hasFoup)
                            {
                                // 有货->有货：货物在传送中
                                if (CurrentState != ActionState.OnTrans)
                                {
                                    CurrentState = ActionState.OnTrans;
                                    PrevState = ActionState.OnTrans;
                                    _Log.OperationLog($"[{PointID}] 货物传送中：FoupID={currentFoupId}");

                                    //UpdateTpCarrier();
                                    //UpdateLocation();
                                    // 更新Shelf控件颜色为黄色（传送中）并显示货物ID
                                }
                                UpdateShelfByPointID(PointID, (int)ShelfColor.ContainGoods, currentFoupId);
                            }
                            else if ((PrevState == ActionState.Entry || PrevState == ActionState.OnTrans) && !hasFoup)
                            {
                                // 有货->无货：货物离开，此时才添加Station
                                _ExitOn = DateTime.Now;
                                CurrentState = ActionState.Exit;
                                PrevState = ActionState.Exit;

                                _Log.OperationLog($"[{PointID}] 货物离开：FoupID={currentFoupId}, 时间={_ExitOn:yyyy-MM-dd HH:mm:ss.fff}");
                                _Log.OperationLog($"[{PointID}] 开始清除状态：准备更新UI为空闲状态");
                                //UpdateTpCarrier();
                                //UpdateLocation();
                                // 更新Shelf控件颜色为默认色（无货物）并清除货物ID
                                UpdateShelfByPointID(PointID, (int)ShelfColor.Empty, null);
                                _Log.OperationLog($"[{PointID}] UI状态已清除：颜色={ShelfColor.Empty}, FoupID=null");

                                var path = GetPathByFoupID(currentFoupId);
                                if (path != null && !stationAdded)
                                {
                                    // 添加当前站点到路径（只添加一次）
                                    path.Stations.Add(this);
                                    stationAdded = true;

                                    if (IsExit)
                                    {
                                        path.ExitOn = _ExitOn;
                                        path.ExitPointID = PointID;
                                        _Log.OperationLog($"[{PointID}] 传送路径完成：FoupID={currentFoupId}, 总耗时={(path.ExitOn - path.EntryOn)?.TotalSeconds:F2}秒");
                                        path.PrintPath();
                                    }
                                    else
                                    {
                                        _Log.OperationLog($"[{PointID}] 中间站点记录：FoupID={currentFoupId}");
                                    }
                                }
                                else if (path == null)
                                {
                                    _Log.ExceptionLog($"[{PointID}] 错误：货物离开时未找到对应传送路径：FoupID={currentFoupId}");
                                }

                                // 重置状态，准备下一次循环
                                currentFoupId = string.Empty;
                                stationAdded = false;
                                _Log.OperationLog($"[{PointID}] 点位状态重置，等待下一个货物");
                            }
                            else if (!hasFoup && (PrevState == ActionState.None || PrevState == ActionState.Exit))
                            {
                                // 无货->无货：空闲状态
                                if (count % 1000 == 0)
                                {
                                    _Log.MemoryLog($"[{PointID}] 点位空闲中...");
                                }
                                UpdateShelfByPointID(PointID, (int)ShelfColor.Empty, foupid);
                            }
                            else
                            {
                                PrevState = ActionState.Entry;
                                UpdateShelfByPointID(PointID, (int)ShelfColor.ContainGoods, foupid);
                            }
                            #endregion

                            count++;
                        }
                        catch (Exception ex)
                        {
                            _Log.ExceptionLog($"[{PointID}] MainLoop异常：{ex.Message}\r\n堆栈：{ex.StackTrace}");
                            System.Threading.Thread.Sleep(1000);
                        }

                        System.Threading.Thread.Sleep(interval);
                    }

                    _Log.OperationLog($"[{PointID}] MainLoop 结束运行");
                });
            }

            /// <summary>
            /// 读取plc 判断是否有货物,如有货物输出货物id到参数
            /// </summary>
            /// <param name="foupid"></param>
            /// <returns></returns>
            public bool CheckHasFoup(out string foupid)
            {
                foupid = string.Empty;
                try
                {
                    var vf = FinClientHelper.GetIOValue($"{_conveyorName}.{Address}.addressF");
                    var vt = FinClientHelper.GetIOValue($"{_conveyorName}.{Address}.addressT");
                    if (vf == null || vt == null)
                        return false;
                    var valF = (short)vf;
                    var valT = (short)vt;

                    if (valF == 0 && valT == 0)// 没有货物
                    {
                        foupid = string.Empty;
                        return false;
                    }
                    foupid = FinClientHelper.GetIOValue($"{_conveyorName}.{Address}.foupid")?.ToString() ?? string.Empty;

                    return true;
                }
                catch (Exception ex)
                {
                    _Log.ExceptionLog(ex.Message + Environment.NewLine + ex.StackTrace);
                    return false;
                }
            }

            /// <summary>
            /// 开始处理
            /// </summary>
            public void Start()
            {
                _IsRun = true;
                MainLoop();
            }

            /// <summary>
            /// 结束处理
            /// </summary>
            public void Stop()
            {
                _IsRun = false; // 修复：应该设置为false来停止MainLoop
                _Log.OperationLog($"[{PointID}] 停止监控");
            }

            // 添加更新Shelf颜色的方法
            private void UpdateShelfByPointID(string pointID, int color)
            {
                UpdateShelfByPointID(pointID, color, null);
            }

            // 添加更新Shelf颜色和货物ID的方法
            private void UpdateShelfByPointID(string pointID, int color, string? foupID)
            {
                try
                {
                    // 通过PointID查找对应的Shelf控件并更新颜色和货物ID
                    _mainForm?.UpdateShelfByPointID(pointID, color, foupID);
                }
                catch (Exception ex)
                {
                    _Log.ExceptionLog($"[{PointID}] 更新Shelf颜色失败：{ex.Message}");
                }
            }
        }

        /// <summary>
        /// 定义一个传送路径,描述完整的传送过程
        /// </summary>
        public class TransmissionPath‌(string foupid, string entryPointID, DateTime? entryOn)
        {
            public Guid Guid { get; } = Guid.NewGuid(); // 唯一标识符
            /// <summary>
            /// 货物ID
            /// </summary>
            public string FoupID { get; set; } = foupid;// Foup ID

            /// <summary>
            /// 进入时间
            /// </summary>
            public DateTime? EntryOn { get; set; } = entryOn;

            /// <summary>
            /// 入口PointID
            /// </summary>
            public string EntryPointID { get; set; } = entryPointID;

            /// <summary>
            /// 过的站点列表
            /// </summary>
            public List<IConveyor> Stations { get; set; } = new List<IConveyor>();

            /// <summary>
            /// 离开时间
            /// </summary>
            public DateTime? ExitOn { get; set; } = null;

            /// <summary>
            /// 离开PointID
            /// </summary>
            public string ExitPointID { get; set; } = string.Empty;

            /// <summary>
            /// 打印输出完整的运行路径
            /// </summary>
            public void PrintPath()
            {
                StringBuilder sb = new StringBuilder();
                sb.AppendLine($"ID: {Guid},FoupID={FoupID}");
                sb.AppendLine($"EntryOn: {EntryOn}, EntryPointID: {EntryPointID}");
                foreach (var station in Stations)
                {
                    sb.AppendLine($"Station - PointID: {station.PointID}, EntryOn: {station.EntryOn}, ExitOn: {station.ExitOn}");
                }
                sb.AppendLine($"ExitOn: {ExitOn}, ExitPointID: {ExitPointID}");
                Console.WriteLine(sb.ToString());
                Proj.Log.Logger.Instance.OperationLog(sb.ToString());
            }
        } 
        #endregion

        // 网格中所有对象列表
        //private readonly List<ObjectContent> objectContents = new List<ObjectContent>();
        private ConcurrentBag<ObjectContent> _objectContents = new ConcurrentBag<ObjectContent>();

        /// <summary>
        /// 记录传送点位处理器
        /// </summary>
        private List<IConveyor> Conveyors = new List<IConveyor>();

        /// <summary>
        /// 日志记录组件
        /// </summary>
        private Logger _Log = Logger.Instance;
        // 选择管理
        private ShelfViewModel? _selectedShelf = null;
        private string? _selectedFoupID = null;
        private int _searchCounter = 0; // 搜索计数器，用于控制主动搜索频率


        // 刷新传送数据定时器
        private DispatcherTimer _RefreshDataTimer = new DispatcherTimer(); 
        // 刷新Shelf定时器
        private DispatcherTimer _RefreshShelfTimer = new DispatcherTimer();

        // 点位数据
        private List<DBEntity.tp_path_point> _PointData = new List<DBEntity.tp_path_point>();

        public frmConvryorMain()
        {
            InitializeComponent();
            this.Loaded += FrmConvryorMain_Loaded;
            this.KeyDown += FrmConvryorMain_KeyDown;
            this.Focusable = true; // 确保窗口可以接收键盘事件
            constr = ConfigurationManager.ConnectionStrings["StkCConnection"].ConnectionString;

            // 设置静态实例
            Instance = this;
        }

        private void FrmConvryorMain_Loaded(object sender, RoutedEventArgs e)
        {
            #region InitDevice
            //PLC通信初始化
            var loadPLCData = PLCTools.Instance.LoadPLCData();
            if (!loadPLCData.code)
            {
                _Log.ExceptionLog("PLC点位数据加载失败! " + loadPLCData.msg);
                Console.WriteLine("PLC点位数据加载失败 " + loadPLCData.msg);
            }
            //连接设备
            var connectEquipment = PLCTools.Instance.ConnectEquipment();
            if (!connectEquipment.code)
            {
                _Log.ExceptionLog("设备连接失败! " + connectEquipment.msg);
                Console.WriteLine("设备连接失败! " + connectEquipment.msg);
            } 
            #endregion

            InitShelf();

            _RefreshDataTimer.Tick += _timer_Elapsed;
            _RefreshShelfTimer.Tick += _RefreshShelfTimer_Elapsed;
            _RefreshDataTimer.Interval = TimeSpan.FromMilliseconds(double.Parse(ConfigurationManager.AppSettings["ShelfRefreshInterval"]));
            _RefreshShelfTimer.Interval = TimeSpan.FromMilliseconds(double.Parse(ConfigurationManager.AppSettings["WarningRefreshInterval"]));
        }

        private void _RefreshShelfTimer_Elapsed(object? sender, EventArgs e)
        {
            Task.Factory.StartNew(() => {
                // 获取警告状态  接口待实现,通过FinClientHelp获取id
                var warningIDs = FinClientHelper.GetIOValue(Get_Warnning_Info) as string;
                // 如果有警告则更新页面元素状态
                if (!warningIDs.IsNullOrEmpty())
                {
                    //地址和警告编码使用|连接起来
                    var tmp = warningIDs.Split(',').ToList();
                    foreach (var id in tmp)
                    {
                        UpdateShelf(id.Split('|')[0], 9);
                    }
                }
                // 从tplocaltion中获取点位信息
                //var warnLocations =GlobalData.dbHelper.tpLocationdb.GetAllTpLocationAsync().Result.Where(p => p.Is_Occupied==1 || p.Is_Reserved==1 || p.Is_Prohibited==1);// 被占用,保留,禁止的点位
                //foreach (var location in warnLocations)
                //{
                //    // 根据点位ID 查找��面元素
                //    var shelf = objectContents.FirstOrDefault(x => string.Compare($"{x.PointInfo.disp_row},{x.PointInfo.disp_column}", location.Address, true) == 0)?.Shelf;
                //    if (shelf == null) continue;
                //    if (location.Is_Prohibited == 1)
                //    {
                //        UpdateShelf(shelf,  9);
                //    }
                //    else if (location.Is_Reserved == 1)
                //    {
                //        UpdateShelf(shelf, 9);
                //    }
                //    else if (location.Is_Occupied == 1)
                //    {
                //        UpdateShelf(shelf, 9);
                //    }
                //}
            });
        }

        private void _timer_Elapsed(object? sender, EventArgs e)
        {
            try
            {
                // CV 利用率 (%) = (有效生产时间 / 总可用时间) × 100%
            }
            catch (Exception ex)
            {
                _Log.ExceptionLog(ex.Message + Environment.NewLine + ex.StackTrace);
            }
        }

        private void InitializeShelf(Shelf shelf, string shelfName, string location, ShelfViewModel? existingViewModel = null)
        {
            ShelfViewModel viewModel;

            if (existingViewModel != null)
            {
                // 使用已存在的ViewModel，只更新必要的属性
                viewModel = existingViewModel;
                viewModel.shelfName = shelfName;
                viewModel.ShelfLocation = location;
                if (viewModel.ShelfColor == null)
                {
                    viewModel.ShelfColor = 0; // 默认颜色
                }
            }
            else
            {
                // 创建新的ShelfViewModel并设置属性
                viewModel = new ShelfViewModel
                {
                    shelfName = shelfName,
                    ShelfLocation = location,
                    ShelfColor = 0 // 默认颜色
                };
            }

            // 将ViewModel绑定到控件
            shelf.DataContext = viewModel;

            // 将ViewModel添加到GlobalData.gbshelfvm字典中
            GlobalData.gbshelfvm.TryAdd(shelfName, viewModel);
        }

        private void InitShelf()
        {
            #region 构造初始数据
            Task.Factory.StartNew(() =>
            {
                // 启动定时器
                _RefreshDataTimer.Start();
                _RefreshShelfTimer.Start();
                _PointData = GlobalData.dbHelper.tpPathPointdb.GetAllTpPathPointAsync().Result;
                if (_PointData == null)
                {
                    return;
                }
                _objectContents.Clear();
                _PointData.ForEach(row =>
                {
                    // 创建ShelfViewModel并设置属性
                    var shelfViewModel = new ShelfViewModel()
                    {
                        ShelfLocation = $"{row.disp_row},{row.disp_column}", // 使用行列格式
                        shelfName = row.address ?? $"P{row.point_id}", // 设置名称
                        ShelfColor = 0, // 初始状态为空闲
                        // 暂时使用硬编码来测试入口出口标记功能
                        IsEntry = row.is_enter, // 测试：点位1和2设为入口
                        IsExit = row.is_exit // 测试：点位15和16设为出口
                    };
                    
                    // 根据表中的数据生成控件
                    var objectContent = new ObjectContent()
                    {
                        PointInfo = row,
                        ControlType = typeof(Shelf),
                        ShelfViewModel = shelfViewModel // 确保设置ViewModel
                    };

                    _objectContents.Add(objectContent);
                });
                
                int rows = _objectContents.Max(p => p.PointInfo.disp_row) + 1;// 最大行数
                int cols = _objectContents.Max(p => p.PointInfo.disp_column) + 1;// 最大列数



                #region 生成表格Grid
                Dispatcher.BeginInvoke(DispatcherPriority.Background, new Action(() =>
                {
                    // 清空现有的Grid内容
                    mainGrid.Children.Clear();
                    mainGrid.RowDefinitions.Clear();
                    mainGrid.ColumnDefinitions.Clear();
                    // 设置Grid的行高和列宽
                    for (int i = 0; i < rows; i++)
                    {
                        mainGrid.RowDefinitions.Add(new RowDefinition() { Height = new GridLength(1, GridUnitType.Star) });
                    }
                    for (int j = 0; j < cols; j++)
                    {
                        mainGrid.ColumnDefinitions.Add(new ColumnDefinition() { Width = new GridLength(1, GridUnitType.Star) });
                    }

                    // 填充单元格内容

                    _objectContents.ToList().ForEach(p =>
                    {
                        if (p.ControlType == typeof(Shelf))
                        {
                            var control = new Shelf() { Margin = new Thickness(5) };
                            p.Shelf = control; // 设置Shelf控件
                                               //InitializeShelf(CV_0_5, "CV_0_5", "0,5");
                            // 使用已存在的ShelfViewModel
                            InitializeShelf(control, $"CV_{p.PointInfo.disp_row}_{p.PointInfo.disp_column}", $"{p.PointInfo.disp_row},{p.PointInfo.disp_column}", p.ShelfViewModel);

                            Grid.SetRow(control, p.PointInfo.disp_row);
                            Grid.SetColumn(control, p.PointInfo.disp_column);
                            mainGrid.Children.Add(control);
                            SubscribeShelfEvent(control);
                        }
                    });
                }));
                #endregion

                #region 启动监控
                _PointData.ForEach(p => {
                    Conveyors.Add(new Conveyor(this) // 传入主窗体引用
                    {
                        PointID = p.point_id.ToString(), // 获取点位ID
                        Address = p.address,
                        // 暂时使用硬编码来测试
                        IsEntry = p.is_enter, // 测试：点位1和2设为入口
                        IsExit = p.is_exit // 测试：点位15和16设为出口
                    });
                });
                // 启动监控
                Conveyors.ForEach(p => p.Start());
                #endregion

            });
            #endregion
        }
        /// <summary>
        /// 刷新shelf状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        /// <exception cref="NotImplementedException"></exception>
        private void _RefreshShelfTimer_Elapsed(object? sender, ElapsedEventArgs e)
        {

        }


        /// <summary>
        /// 发送semi事件
        /// </summary>
        private void SemiSend()
        { }

        /// <summary>
        /// 更新Shelf控件的颜色
        /// </summary>
        /// <param name="id"></param>
        /// <param name="color"></param>
        private void UpdateShelf(string id, int color)
        {
            // 根据点位ID 查找页面元素
            var shelf = _objectContents.FirstOrDefault(x => string.Compare(x.PointInfo.address, id, true) == 0)?.Shelf;
            UpdateShelf(shelf,color);
        }
        private void UpdateShelf(Shelf shelf, int color)
        {
            Dispatcher.BeginInvoke(DispatcherPriority.Background, new Action(() =>
            {
                if (shelf != null)
                {
                    var model = shelf.DataContext as ShelfViewModel;
                    if (model != null)
                    {
                        model.ShelfColor = color; // 更新
                    }
                }
            }));

        }
        private void SubscribeShelfEvent(Shelf shelf)
        {
            // 订阅Shelf控件的左键点击事件
            shelf.ShelfLeftClick += (sender, vm) =>
            {
                // 处理选择功能
                if (vm != null)
                {
                    // 如果点击的是已选中的Shelf，则取消选择
                    if (_selectedShelf == vm)
                    {
                        ClearSelection();
                    }
                    else
                    {
                        // 选择新的Shelf
                        SelectShelf(vm);
                    }
                }

                // 获取控件名称
                string controlName = ((Shelf)sender).Name;
                // 调用GlobalData中的方法处理左键点击事件
                GlobalData.ControlleftClick(controlName);
            };

            // 订阅Shelf控件的双击事件
            shelf.shelfDbClick += (sender, vm) =>
            {
                var dataContext = (sender as Shelf).DataContext as ShelfViewModel;
                // 获取控件名称
                string controlName = ((Shelf)sender).Name;
                // 调用GlobalData中的方法处理双击事件
                GlobalData.ShelfleftDbClick(dataContext.ShelfLocation, vm);
            };
        }
        /// <summary>
        /// 根据PointID更新Shelf控件的颜色
        /// </summary>
        /// <param name="pointID">点位ID</param>
        /// <param name="color">颜色代码</param>
        public void UpdateShelfByPointID(string pointID, int color)
        {
            UpdateShelfByPointID(pointID, color, null);
        }

        /// <summary>
        /// 测试UI绑定（调试用）
        /// </summary>
        public void TestUIBinding()
        {
            _Log.OperationLog("开始测试UI绑定");

            foreach (var objectContent in _objectContents.Take(3)) // 只测试前3个
            {
                if (objectContent?.ShelfViewModel != null)
                {
                    var vm = objectContent.ShelfViewModel;
                    var pointId = objectContent.PointInfo.point_id.ToString();

                    _Log.OperationLog($"[测试] 点位 {pointId}: 当前颜色={vm.ShelfColor}, 当前FoupID={vm.FoupID ?? "null"}");

                    // 测试颜色和货物ID变化
                    var newColor = vm.ShelfColor == 0 ? 1 : 0;
                    var newFoupID = vm.FoupID == null ? "TEST123" : null;

                    // 使用优化后的UpdateShelfByPointID方法进行测试
                    UpdateShelfByPointID(pointId, newColor, newFoupID);

                    _Log.OperationLog($"[测试] 点位 {pointId}: 更新后颜色={vm.ShelfColor}, 更新后FoupID={vm.FoupID ?? "null"}");
                }
            }

            _Log.OperationLog("UI绑定测试完成");
        }

        /// <summary>
        /// 手动清除所有点位状态（调试用）
        /// </summary>
        public void ClearAllShelfStates()
        {
            _Log.OperationLog("开始手动清除所有点位状态");

            // 使用优化后的UpdateShelfByPointID方法进行批量清除
            foreach (var objectContent in _objectContents)
            {
                if (objectContent?.ShelfViewModel != null)
                {
                    var pointId = objectContent.PointInfo.point_id.ToString();
                    var vm = objectContent.ShelfViewModel;

                    // 只有当前状态不是空闲时才需要清除
                    if (vm.ShelfColor != (int)ShelfColor.Empty || !string.IsNullOrEmpty(vm.FoupID))
                    {
                        UpdateShelfByPointID(pointId, (int)ShelfColor.Empty, null);
                        _Log.OperationLog($"[手动清除] 点位 {pointId}({vm.ShelfLocation}) 已清除");
                    }
                }
            }

            _Log.OperationLog("手动清除所有点位状态完成");
        }

        /// <summary>
        /// 根据PointID获取ObjectContent
        /// </summary>
        /// <param name="pointID">点位ID</param>
        /// <returns>对应的ObjectContent，如果未找到则返回null</returns>
        public ObjectContent GetObjectContentByPointID(string pointID)
        {
            try
            {
                return _objectContents.FirstOrDefault(x =>
                    string.Compare(x.PointInfo.point_id.ToString(), pointID, true) == 0);
            }
            catch (Exception ex)
            {
                _Log.ExceptionLog($"获取ObjectContent失败: PointID={pointID}, Error={ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 根据PointID获取Conveyor
        /// </summary>
        /// <param name="pointID">点位ID</param>
        /// <returns>对应的Conveyor，如果未找到则返回null</returns>
        public IConveyor GetConveyorByPointID(string pointID)
        {
            try
            {
                return Conveyors.FirstOrDefault(x =>
                    string.Compare(x.PointID, pointID, true) == 0);
            }
            catch (Exception ex)
            {
                _Log.ExceptionLog($"获取Conveyor失败: PointID={pointID}, Error={ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 安全的UI更新方法（带超时保护）
        /// </summary>
        private void SafeUpdateUI(Action uiAction, string context = "")
        {
            try
            {
                if (Application.Current?.Dispatcher == null) return;

                // 检查是否在UI线程
                if (Application.Current.Dispatcher.CheckAccess())
                {
                    // 已在UI线程，直接执行
                    uiAction();
                }
                else
                {
                    // 在后台线程，使用异步调用
                    Application.Current.Dispatcher.BeginInvoke(uiAction, System.Windows.Threading.DispatcherPriority.Background);
                }
            }
            catch (Exception ex)
            {
                _Log.ExceptionLog($"SafeUpdateUI异常 [{context}]: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据PointID更新Shelf控件的颜色和货物ID
        /// </summary>
        /// <param name="pointID">点位ID</param>
        /// <param name="color">颜色代码</param>
        /// <param name="foupID">货物ID</param>
        public void UpdateShelfByPointID(string pointID, int color, string? foupID)
        {
            // 根据PointID查找对应的ObjectContent
            var objectContent = _objectContents.FirstOrDefault(x =>
                string.Compare(x.PointInfo.point_id.ToString(), pointID, true) == 0);

            if (objectContent?.ShelfViewModel == null)
            {
                _Log.OperationLog($"[警告] 未找到点位 {pointID} 对应的ShelfViewModel");
                return;
            }

            var vm = objectContent.ShelfViewModel;
            var currentColor = vm.ShelfColor;
            var currentFoupID = vm.FoupID;

            // 🚀 性能优化：检查状态是否真的需要更新
            bool colorChanged = currentColor != color;
            bool foupIDChanged = !string.Equals(currentFoupID, foupID, StringComparison.Ordinal);

            if (!colorChanged && !foupIDChanged)
            {
                // 状态没有变化，直接返回，避免不必要的UI更新
                return;
            }

            // 只有状态真正发生变化时才进行UI更新
            SafeUpdateUI(() =>
            {
                // 更新属性值
                if (colorChanged)
                {
                    vm.ShelfColor = color;
                }
                if (foupIDChanged)
                {
                    vm.FoupID = foupID;
                }

                // 记录状态变化（只记录真正发生变化的部分）
                if (colorChanged || foupIDChanged)
                {
                    _Log.OperationLog($"[UI更新] 点位 {pointID}({vm.ShelfLocation}) " +
                        $"{(colorChanged ? $"颜色: {currentColor} → {color}" : "")}" +
                        $"{(colorChanged && foupIDChanged ? ", " : "")}" +
                        $"{(foupIDChanged ? $"货物: {currentFoupID ?? "无"} → {foupID ?? "无"}" : "")}");
                }

                // 只刷新发生变化的属性，减少不必要的通知
                if (colorChanged)
                {
                    vm.NotifyPropertyChanged(nameof(vm.ShelfColor));
                    vm.NotifyPropertyChanged(nameof(vm.ShelfColorBrush));
                    vm.NotifyPropertyChanged(nameof(vm.StatusText));
                }
                if (foupIDChanged)
                {
                    vm.NotifyPropertyChanged(nameof(vm.FoupID));
                    vm.NotifyPropertyChanged(nameof(vm.FoupDisplayText));
                    vm.NotifyPropertyChanged(nameof(vm.StatusText));
                }

                // 验证更新结果（仅在调试模式下记录详细信息）
                #if DEBUG
                _Log.OperationLog($"[UI验证] 点位 {pointID} 更新后状态: 颜色={vm.ShelfColor}, FoupID={vm.FoupID ?? "null"}, 显示文本='{vm.FoupDisplayText}'");
                #endif

                // 检查选择跟踪（仅在货物ID发生变化时）
                if (foupIDChanged)
                {
                    CheckSelectionTracking(vm, foupID);
                    CheckCarrierExitFromAnyExit(vm, currentFoupID, foupID);
                    CheckAndUpdateCarrierLocation();
                }
            }, $"UpdateShelfByPointID-{pointID}");
        }

        /// <summary>
        /// 检查选择跟踪
        /// </summary>
        /// <param name="shelfViewModel">当前更新的Shelf</param>
        /// <param name="foupID">货物ID</param>
        private void CheckSelectionTracking(ShelfViewModel shelfViewModel, string? foupID)
        {
            // 如果当前选中的Shelf有货物，并且货物移动到了新位置
            if (_selectedShelf != null && !string.IsNullOrEmpty(_selectedFoupID))
            {
                // 情况1：货物移动到了新的Shelf
                if (!string.IsNullOrEmpty(foupID) && foupID == _selectedFoupID && shelfViewModel != _selectedShelf)
                {
                    _Log.OperationLog($"[跟踪成功] 货物 {foupID} 从 {_selectedShelf.ShelfLocation} 移动到 {shelfViewModel.ShelfLocation}");

                    // 取消原来的选择
                    _selectedShelf.IsSelected = false;
                    _selectedShelf.RefreshSelectionDisplay();

                    // 选择新的Shelf
                    _selectedShelf = shelfViewModel;
                    _selectedShelf.IsSelected = true;
                    _selectedShelf.RefreshSelectionDisplay();
                }
                // 情况2：货物离开了当前选中的Shelf
                else if (shelfViewModel == _selectedShelf && string.IsNullOrEmpty(foupID))
                {
                    _Log.OperationLog($"[跟踪] 货物 {_selectedFoupID} 离开位置 {shelfViewModel.ShelfLocation}");

                    // 如果货物从出口离开，取消选择
                    if (shelfViewModel.IsExit)
                    {
                        _Log.OperationLog($"[出口离开] 货物 {_selectedFoupID} 从出口 {shelfViewModel.ShelfLocation} 离开，取消选择");
                        ClearSelection();
                    }
                }
            }
        }

        /// <summary>
        /// 检查货物是否从任意出口离开
        /// </summary>
        private void CheckCarrierExitFromAnyExit(ShelfViewModel shelfViewModel, string? oldFoupID, string? newFoupID)
        {
            // 如果有选中的货物，并且某个出口点位失去了我们跟踪的货物
            if (_selectedShelf != null && !string.IsNullOrEmpty(_selectedFoupID) &&
                shelfViewModel.IsExit && !string.IsNullOrEmpty(oldFoupID) &&
                string.IsNullOrEmpty(newFoupID) && oldFoupID == _selectedFoupID)
            {
                _Log.OperationLog($"[出口离开] 跟踪的货物 {_selectedFoupID} 从出口 {shelfViewModel.ShelfLocation} 离开，取消选择");
                ClearSelection();
            }
        }

        /// <summary>
        /// 检查并更新货物位置（主动搜索）
        /// </summary>
        private void CheckAndUpdateCarrierLocation()
        {
            // 控制搜索频率，每50次更新执行一次主动搜索（减少频率）
            _searchCounter++;
            if (_searchCounter < 50) return;
            _searchCounter = 0;

            // 如果有选中的货物但当前选中的Shelf没有该货物，主动搜索
            if (_selectedShelf != null && !string.IsNullOrEmpty(_selectedFoupID) &&
                _selectedShelf.FoupID != _selectedFoupID)
            {
                // 在所有Shelf中搜索我们跟踪的货物
                var shelfWithCarrier = _objectContents.FirstOrDefault(oc =>
                    oc.ShelfViewModel != null &&
                    !string.IsNullOrEmpty(oc.ShelfViewModel.FoupID) &&
                    oc.ShelfViewModel.FoupID == _selectedFoupID);

                if (shelfWithCarrier?.ShelfViewModel != null && shelfWithCarrier.ShelfViewModel != _selectedShelf)
                {
                    _Log.OperationLog($"[主动搜索] 货物 {_selectedFoupID} 从 {_selectedShelf.ShelfLocation} 移动到 {shelfWithCarrier.ShelfViewModel.ShelfLocation}");

                    // 取消原来的选择
                    _selectedShelf.IsSelected = false;
                    _selectedShelf.RefreshSelectionDisplay();

                    // 选择新的Shelf
                    _selectedShelf = shelfWithCarrier.ShelfViewModel;
                    _selectedShelf.IsSelected = true;
                    _selectedShelf.RefreshSelectionDisplay();
                }
            }
        }

        /// <summary>
        /// 选择Shelf
        /// </summary>
        /// <param name="shelfViewModel">要选择的Shelf</param>
        public void SelectShelf(ShelfViewModel shelfViewModel)
        {
            // 取消之前的选择
            if (_selectedShelf != null)
            {
                _selectedShelf.IsSelected = false;
                _selectedShelf.RefreshSelectionDisplay();
            }

            // 设置新的选择
            _selectedShelf = shelfViewModel;
            _selectedShelf.IsSelected = true;
            _selectedShelf.RefreshSelectionDisplay();

            // 如果选中的Shelf有货物，记录货物ID用于跟踪
            _selectedFoupID = shelfViewModel.FoupID;

            _Log.OperationLog($"选择Shelf：{shelfViewModel.ShelfLocation}, 货物ID：{_selectedFoupID ?? "无"}");
        }

        /// <summary>
        /// 取消选择
        /// </summary>
        public void ClearSelection()
        {
            if (_selectedShelf != null)
            {
                _selectedShelf.IsSelected = false;
                _selectedShelf.RefreshSelectionDisplay();
                _selectedShelf = null;
                _selectedFoupID = null;
                _Log.OperationLog("取消Shelf选择");
            }
        }

        /// <summary>
        /// 检查是否有被选中且含有货物的Shelf
        /// </summary>
        public bool HasSelectedCarrierShelf()
        {
            return _selectedShelf != null && !string.IsNullOrEmpty(_selectedFoupID);
        }

        /// <summary>
        /// 获取选中的货物信息
        /// </summary>
        public (string FoupID, string Location) GetSelectedCarrierInfo()
        {
            if (_selectedShelf != null && !string.IsNullOrEmpty(_selectedFoupID))
            {
                return (_selectedFoupID, _selectedShelf.ShelfLocation ?? "未知");
            }
            return (string.Empty, string.Empty);
        }



        /// <summary>
        /// 根据ShelfLocation获取对应的point_id
        /// </summary>
        public int GetPointIdFromShelfLocation(string shelfLocation)
        {
            try
            {
                if (string.IsNullOrEmpty(shelfLocation))
                    return 0;

                // 从_PointData中查找对应的point_id
                var pointData = _PointData.FirstOrDefault(p =>$"{p.disp_row},{p.disp_column}" == shelfLocation);

                return pointData?.point_id ?? 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// 键盘事件处理
        /// </summary>
        private void FrmConvryorMain_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.Escape:
                    // 按Esc键取消选择
                    ClearSelection();
                    e.Handled = true;
                    break;
            }
        }

        /// <summary>
        /// 获取当前选中的Shelf信息
        /// </summary>
        /// <returns>选中的Shelf信息，如果没有选中则返回null</returns>
        public (ShelfViewModel? Shelf, string? FoupID) GetSelectedShelfInfo()
        {
            return (_selectedShelf, _selectedFoupID);
        }

        /// <summary>
        /// 根据点位ID选择Shelf
        /// </summary>
        /// <param name="pointID">点位ID</param>
        /// <returns>是否成功选择</returns>
        public bool SelectShelfByPointID(string pointID)
        {
            var objectContent = _objectContents.FirstOrDefault(x =>
                string.Compare(x.PointInfo.point_id.ToString(), pointID, true) == 0);

            if (objectContent?.ShelfViewModel != null)
            {
                SelectShelf(objectContent.ShelfViewModel);
                return true;
            }
            return false;
        }
    }
}
