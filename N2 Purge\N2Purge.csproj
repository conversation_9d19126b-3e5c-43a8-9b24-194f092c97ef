﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <RootNamespace>N2Purge</RootNamespace>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <Folder Include="Examples\" />
    <Folder Include="Image\" />
    <Folder Include="WindormControl\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="LiveCharts.Wpf" Version="0.9.7" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.2" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="OxyPlot.Wpf" Version="2.2.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\poc\Emulator-master-800418e150038863d51594993d633fd6dd01472b\Emulator-master-800418e150038863d51594993d633fd6dd01472b\EmulatorClient\EmulatorClient\EmulatorClient.csproj" />
    <ProjectReference Include="..\CSVTools\CSVTools.csproj" />
    <ProjectReference Include="..\DBEntity\DBEntity.csproj" />
    <ProjectReference Include="..\PlcTools\PlcTools.csproj" />
    <ProjectReference Include="..\Proj.Log\Proj.Log.csproj" />
    <ProjectReference Include="..\Proj.RecordReplay\Proj.RecordReplay.csproj" />

    <ProjectReference Include="..\WCFClient\WCFClient.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Xceed.Wpf.Toolkit">
      <HintPath>..\Dll\Xceed.Wpf.Toolkit.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Update="Config\Log4net.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
