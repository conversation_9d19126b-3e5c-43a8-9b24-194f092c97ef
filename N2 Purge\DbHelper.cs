using DBEntity;
using DBEntity.History;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace N2Purge
{
    public class DbHelper
    {
        private static string connectionString = ConfigurationManager.ConnectionStrings["StkCConnection"].ConnectionString;
       // private static string connectionString = "Server=10.201.100.100;Port=3306;Database=stkc;Uid=root;Pwd=********;Charset=utf8;Convert Zero Datetime=True";
        #region
        public UserManagement? _userManagement;
        public TpLocationExample? tpLocationdb;
        public TpCarrierExample? tpCarrierdb;
        public AlarmHistoryExample? thAlarmdb;
        public TpTransferExample? transferdb;
        public TpAlarmExample? tpAlarmdb;
        public TpCraneExample? tpCranedb;
        public TpAlarmConfigExample? tpAlarmConfig; 
        public EqpHistoryExample? thEqpdb;
        public TpCyclecarrierExample? tpcyclecarrierdb;
        public TransferHistoryExample? thtransferdb;
        public PioHistoryExample? thpiodb;
        public TpPortExample? tpport;
        public TpCyclelocationExample tpcycleLocdb;
        public TpZoneExample? tpzonedb;
        public TpPointConfigExample? tpPointConfigdb;
        public TpPathPointExample? tpPathPointdb;

        public DbHelper() 
        {
            tpLocationdb=new TpLocationExample(connectionString);
            tpCarrierdb=new TpCarrierExample(connectionString);
            thAlarmdb = new AlarmHistoryExample(connectionString);
            transferdb=new TpTransferExample(connectionString); 
            tpAlarmdb = new TpAlarmExample(connectionString);
            tpCranedb=new TpCraneExample(connectionString);
            tpAlarmConfig=new TpAlarmConfigExample(connectionString);   
            thEqpdb=new EqpHistoryExample(connectionString);
            _userManagement = new UserManagement(connectionString);
            tpcyclecarrierdb=new TpCyclecarrierExample(connectionString);
            thtransferdb=new TransferHistoryExample(connectionString);
            thpiodb=new PioHistoryExample(connectionString);
            tpport=new TpPortExample(connectionString);
            tpcycleLocdb = new  (connectionString);
            tpzonedb = new TpZoneExample(connectionString);
            tpPointConfigdb = new TpPointConfigExample(connectionString);
            tpPathPointdb = new TpPathPointExample(connectionString);
        }
        #endregion
    }
}
