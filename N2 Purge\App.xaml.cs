﻿using N2Purge.Services;
using Proj.Log;
using System.Configuration;
using System.Data;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Windows;
using System.Windows.Threading;

namespace N2Purge
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        static ManualResetEvent resetEvent = new ManualResetEvent(false);
        public App()
        {
            // 检查局域网中是否有其他应用程序在运行
            Task.Factory.StartNew(() =>
            {
                try
                {
                    while (!CheckAuth(resetEvent))
                    {
                        Console.WriteLine("检测到其他应用程序正在运行，等待3秒后重试...");
                        Thread.Sleep(3000);
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"发生异常: {ex.Message}");
                }
            });

            resetEvent.WaitOne();

            GlobalExceptionHandler.Initialize();
        }

        #region 检查局域网中是否有其他应用程序在运行
        /// <summary>
        /// 检查局域网中是否有其他应用程序在运行
        /// </summary>
        /// <param name="resetEvent"></param>
        private static bool CheckAuth(ManualResetEvent resetEvent)
        {
            var udpClient = new UdpClient();
            udpClient.EnableBroadcast = true;
            var localIP = GetLocalIPAddress();
            var broadcastAddress = new IPEndPoint(IPAddress.Broadcast, 50000);
            var message = Encoding.UTF8.GetBytes(localIP);
            var receiveClient = new UdpClient(50000);
            receiveClient.Client.ReceiveTimeout = 3000; // 3秒超时

            bool receivedReply = false;

            while (!receivedReply)
            {
                udpClient.Send(message, message.Length, broadcastAddress);
                Console.WriteLine($"广播内容: {localIP}");

                try
                {
                    var remoteEP = new IPEndPoint(IPAddress.Any, 0);
                    var data = receiveClient.Receive(ref remoteEP);
                    var reply = Encoding.UTF8.GetString(data);

                    // 判断回复是否来自本机
                    if (remoteEP.Address.ToString() != localIP && reply != localIP)
                    {
                        receivedReply = true;
                        Console.WriteLine($"收到回复: {reply}");
                        break;
                    }
                }
                catch (SocketException)
                {
                    // 没有收到回复
                    resetEvent.Set();
                }

                Thread.Sleep(3 * 1000);
            }

            udpClient.Close();
            receiveClient.Close();
            return receivedReply;
        } 
        #endregion

        #region 获取本地IP地址
        /// <summary>
        /// 获取本地IP地址
        /// </summary>
        /// <returns></returns>
        private static string GetLocalIPAddress()
        {
            var host = Dns.GetHostEntry(Dns.GetHostName());
            foreach (var ip in host.AddressList)
            {
                if (ip.AddressFamily == AddressFamily.InterNetwork)
                {
                    return ip.ToString();
                }
            }
            return "127.0.0.1";
        } 
        #endregion
    }

}
