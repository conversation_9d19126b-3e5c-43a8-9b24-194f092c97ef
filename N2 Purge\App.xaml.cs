﻿using Proj.Log;
using System.Configuration;
using System.Data;
using System.Windows;
using System.Windows.Threading;

namespace N2Purge
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        public App()
        {
            // UI线程未捕获异常
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;

            // 非UI线程未捕获异常（比如后台线程）
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            // Task 任务中未捕获异常
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;
        }
        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            MessageBox.Show("发生了未处理的异常（UI线程）：" + e.Exception.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);

            // 可记录日志到文件
            // Log(e.Exception);
            Logger.Instance.ExceptionLog(e.Exception.Message + Environment.NewLine + e.Exception.StackTrace);

            // 防止程序崩溃
            e.Handled = true;
        }
        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Exception ex = e.ExceptionObject as Exception;
            Logger.Instance.ExceptionLog(ex.Message + Environment.NewLine + ex.StackTrace);
            MessageBox.Show("发生了未处理的异常（非UI线程）：" + ex?.Message, "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);

            // 一般不能阻止程序崩溃，所以建议记录日志
            // Log(ex);
        }
        private void TaskScheduler_UnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            MessageBox.Show("任务异常未被捕获：" + e.Exception.Message, "任务错误", MessageBoxButton.OK, MessageBoxImage.Warning);

            // 防止程序因未观察异常崩溃
            e.SetObserved();
        }
    }

}
